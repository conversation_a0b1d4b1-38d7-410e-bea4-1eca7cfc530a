@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Etherchain AI Theme */
:root {
  --gradient-primary: linear-gradient(90deg, #512da8 -20%, #673ab7 30%, #e91e63 85%);
  --gradient-hover: linear-gradient(90deg, #9333ea, #3b82f6);
}

/* Custom gradient classes */
.bg-gradient-etherchain {
  background: var(--gradient-primary);
}

.bg-gradient-etherchain-hover {
  background: var(--gradient-hover);
}

/* Blob animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom button styles */
.btn-etherchain {
  background: var(--gradient-primary);
  transition: all 0.3s ease;
}

.btn-etherchain:hover {
  background: var(--gradient-hover);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Presale widget floating animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(5deg);
  }
}

@keyframes floatReverse {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(10px) rotate(-3deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-reverse {
  animation: floatReverse 8s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out;
}

/* Mobile Menu Animations */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.mobile-menu-enter {
  animation: slideInLeft 0.3s ease-out;
}

.mobile-menu-exit {
  animation: slideOutLeft 0.3s ease-in;
}

.overlay-enter {
  animation: fadeIn 0.3s ease-out;
}

.overlay-exit {
  animation: fadeOut 0.3s ease-in;
}

/* Mobile menu item animations */
.mobile-menu-item {
  opacity: 0;
  transform: translateX(-20px);
  animation: slideInMenuItem 0.4s ease-out forwards;
}

.mobile-menu-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-menu-item:nth-child(2) { animation-delay: 0.2s; }
.mobile-menu-item:nth-child(3) { animation-delay: 0.3s; }
.mobile-menu-item:nth-child(4) { animation-delay: 0.4s; }
.mobile-menu-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInMenuItem {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Lightchain.ai inspired navigation hover effects */
.nav-link {
  position: relative;
  display: inline-block;
  padding: 12px 0;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

/* Create the underline effect */
.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ff3bd4, transparent);
  transform: translateX(-100%);
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #ff3bd4;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 10px rgba(255, 59, 212, 0.5);
}

/* Hover animations */
.nav-link:hover::before {
  transform: translateX(100%);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-link:hover {
  color: #ff3bd4 !important;
  text-shadow: 0 0 8px rgba(255, 59, 212, 0.3);
  transform: translateY(-1px);
}

/* Add a subtle glow effect on hover */
.nav-link:hover {
  filter: drop-shadow(0 0 8px rgba(255, 59, 212, 0.2));
}

/* Enhanced navigation styling for premium look */
nav {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Dropdown menu enhanced styling */
.nav-dropdown {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

@media (prefers-color-scheme: dark) {
  .nav-dropdown {
    background: rgba(17, 24, 39, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
}

/* Dropdown item hover effects */
.nav-dropdown-item {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.nav-dropdown-item:hover::before {
  transform: translateX(0);
}

.nav-dropdown-item:hover {
  background: rgba(147, 51, 234, 0.1);
  color: #9333ea;
}

/* Enhanced button styles */
.nav-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(147, 51, 234, 0.3);
}

/* Dropdown animations */
.nav-dropdown {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects for various elements */
.hover-accent:hover {
  color: #9333ea;
  transform: translateY(-1px);
}

.border-accent:hover {
  border-color: #9333ea;
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
}

.bg-accent:hover {
  background: linear-gradient(90deg, #9333ea 20%, #7c3aed 50%, #ec4899 80%);
  transform: translateY(-2px);
}

/* Custom button styles */
.btn-accent {
  background: linear-gradient(90deg, #9333ea 20%, #7c3aed 50%, #ec4899 80%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-accent:hover::before {
  left: 100%;
}

.btn-accent:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(147, 51, 234, 0.4);
}

.btn-accent-active {
  background: linear-gradient(90deg, #7c3aed 20%, #6d28d9 50%, #db2777 80%);
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
  transform: translateY(-1px);
}

/* Footer link hover effects */
.footer-link:hover {
  color: #9333ea;
  transform: translateX(5px);
}

/* Social link hover effects */
.social-link:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.4);
}

/* Light mode text colors */
.text-gray-700 {
  color: #374151;
}

.text-gray-600 {
  color: #4b5563;
}

/* Dark mode text colors */
@media (prefers-color-scheme: dark) {
  .text-gray-700 {
    color: #d1d5db;
  }

  .text-gray-600 {
    color: #9ca3af;
  }

  .text-gray-500 {
    color: #6b7280;
  }

  .text-gray-400 {
    color: #9ca3af;
  }
}

/* Glass effect for cards and containers */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Brand text styling */
.brand-text {
  background: linear-gradient(90deg, #9333ea 20%, #7c3aed 50%, #ec4899 80%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-text:hover {
  background: linear-gradient(90deg, #7c3aed 20%, #6d28d9 50%, #db2777 80%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@media (prefers-color-scheme: dark) {
  .brand-text {
    background: linear-gradient(90deg, #a855f7 20%, #9333ea 50%, #ec4899 80%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .brand-text:hover {
    background: linear-gradient(90deg, #9333ea 20%, #7c3aed 50%, #db2777 80%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Responsive logo sizing */
@media (max-width: 640px) {
  .logo-container .logo-image {
    width: 2.5rem;
    height: 2.5rem;
  }

  .logo-container .brand-text {
    font-size: 1.25rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .logo-container .logo-image {
    width: 3rem;
    height: 3rem;
  }

  .logo-container .brand-text {
    font-size: 1.5rem;
  }
}

@media (min-width: 769px) {
  .logo-container .logo-image {
    width: 3.5rem;
    height: 3.5rem;
  }

  .logo-container .brand-text {
    font-size: 1.875rem;
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #111827;
    --foreground: #f9fafb;
  }
}

/* Light mode specific styles */
:root {
  --background: #ffffff;
  --foreground: #111827;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #111827;
    --foreground: #f9fafb;
  }
}

/* Base styles */
html,
body {
  background: var(--background);
  color: var(--foreground);
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
}

.heading-gradient {
  background: linear-gradient(90deg, #9333ea 20%, #7c3aed 50%, #ec4899 80%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@media (prefers-color-scheme: dark) {
  .heading-gradient {
    background: linear-gradient(90deg, #a855f7 20%, #9333ea 50%, #ec4899 80%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Form elements */
button, .btn {
  font-family: 'Inter', sans-serif;
  transition: all 0.3s ease;
}

nav a, .nav-link {
  font-family: 'Inter', sans-serif;
  transition: all 0.3s ease;
}

/* Input number styling */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* Token icon styling */
.token-icon {
  border-radius: 50%;
}

@media (prefers-color-scheme: light) {
  .token-icon.eth {
    color: #3b82f6;
  }

  .token-icon.usdt {
    color: #10b981;
  }
}

@media (prefers-color-scheme: dark) {
  .token-icon.eth {
    color: #60a5fa;
  }

  .token-icon.usdt {
    color: #34d399;
  }
}

/* Etherchain logo styling */
.etherchain-logo {
  filter: brightness(1);
}

@media (prefers-color-scheme: dark) {
  .etherchain-logo {
    filter: brightness(1.2);
  }
}

.etherchain-logo-container {
  border-radius: 50%;
}

@media (prefers-color-scheme: dark) {
  .etherchain-logo-container {
    padding: 2px;
  }
}

/* Body background */
body {
  background: #f9fafb;
  color: #111827;
}

@media (prefers-color-scheme: dark) {
  body {
    background: #111827;
    color: #f9fafb;
  }
}

/* Global styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

a {
  text-decoration: none;
  color: inherit;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* ReferralModal Global Styles - Enhanced */
.referral-modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 99999 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 1rem !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
}

.referral-modal-content {
  position: relative !important;
  z-index: 100000 !important;
  max-width: 28rem !important;
  width: 100% !important;
}

/* Ensure modal is always on top */
[data-referral-modal="true"] {
  z-index: 99999 !important;
}

[data-referral-modal="true"] > * {
  z-index: 100000 !important;
}

/* Force modal to be on top of everything */
body > div[style*="z-index: 99999"] {
  z-index: 99999 !important;
}

/* Override any other z-index values */
div[style*="position: fixed"][style*="z-index: 99999"] {
  z-index: 99999 !important;
}

/* Ensure modal content is above overlay */
div[style*="position: fixed"][style*="z-index: 99999"] > div {
  z-index: 100000 !important;
}

/* ReferralModal Close Button - Ensure it's always clickable */
[data-referral-modal="true"] button[onClick] {
  pointer-events: auto !important;
  z-index: 100001 !important;
}

[data-referral-modal="true"] button[onClick]:hover {
  pointer-events: auto !important;
}

/* Specific close button styling */
.referral-modal-close-button {
  pointer-events: auto !important;
  z-index: 100001 !important;
  cursor: pointer !important;
}

/* GradientButton Dark Theme Support */
.dark .bg-gray-100 {
  background-color: #0a0a0a !important;
}

.elementToFadeInAndOut {
  -webkit-animation: fadeinout 4s linear forwards;
  animation: fadeinout 4s linear forwards;
  opacity: 0;
}

@-webkit-keyframes fadeinout {
50% { opacity: 1; }
}

@keyframes fadeinout {
50% { opacity: 1; }
}