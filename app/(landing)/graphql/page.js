'use client';

import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faCode,
  faDatabase,
  faRocket,
  faClock,
  faLightbulb,
  faPlay
} from '@fortawesome/free-solid-svg-icons';

export default function GraphQLPage() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <FontAwesomeIcon icon={faDatabase} className="mr-3 text-purple-600 dark:text-purple-400" />
                GraphQL Playground
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Interactive GraphQL playground for querying blockchain data
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Coming Soon Banner */}
        <div className="text-center mb-12">
          <div className="bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 border border-purple-300 dark:border-purple-500/30 rounded-2xl p-12 mb-8 shadow-sm">
            <div className="mb-6">
              <FontAwesomeIcon 
                icon={faRocket} 
                className="text-6xl text-purple-600 dark:text-purple-400 mb-4 animate-pulse" 
              />
            </div>
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Coming Soon
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Our interactive GraphQL playground is currently under development. 
              Get ready to explore blockchain data with powerful queries.
            </p>
            <div className="flex items-center justify-center text-gray-500 dark:text-gray-400 mb-6">
              <FontAwesomeIcon icon={faClock} className="mr-2" />
              <span>Expected Launch: Q4 2025</span>
            </div>
          </div>
        </div>

        {/* Features Preview */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:border-purple-500/50 transition-colors shadow-sm">
            <div className="flex items-center mb-4">
              <FontAwesomeIcon icon={faPlay} className="text-purple-600 dark:text-purple-400 text-xl mr-3" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Interactive Playground</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Real-time GraphQL playground with syntax highlighting and auto-completion
            </p>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:border-purple-500/50 transition-colors shadow-sm">
            <div className="flex items-center mb-4">
              <FontAwesomeIcon icon={faDatabase} className="text-blue-600 dark:text-blue-400 text-xl mr-3" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Blockchain Data</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Query transactions, blocks, accounts, and smart contract data
            </p>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:border-purple-500/50 transition-colors shadow-sm">
            <div className="flex items-center mb-4">
              <FontAwesomeIcon icon={faLightbulb} className="text-yellow-600 dark:text-yellow-400 text-xl mr-3" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Query Examples</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Pre-built query templates and examples for common use cases
            </p>
          </div>
        </div>

        {/* Search Section */}
        <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 mb-8 shadow-sm">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faSearch} className="text-gray-500 dark:text-gray-400 mr-3" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Search GraphQL Schema</h3>
          </div>
          <div className="relative">
            <input
              type="text"
              placeholder="Search for types, fields, or queries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
            />
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-white">
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </div>
        </div>

        {/* Quick Links */}
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Getting Started</h3>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li>• GraphQL Basics</li>
              <li>• Schema Documentation</li>
              <li>• Query Examples</li>
              <li>• Best Practices</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Available Queries</h3>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li>• Transaction Data</li>
              <li>• Block Information</li>
              <li>• Account Details</li>
              <li>• Token Transfers</li>
            </ul>
          </div>
        </div>

        {/* Sample Query Preview */}
        <div className="mt-12">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Sample Query Preview</h3>
          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <div className="flex items-center mb-4">
              <FontAwesomeIcon icon={faCode} className="text-purple-600 dark:text-purple-400 mr-3" />
              <span className="text-gray-600 dark:text-gray-400 font-mono">Sample GraphQL Query</span>
            </div>
            <pre className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-sm text-gray-850 dark:text-gray-200 overflow-x-auto">
{`query GetLatestTransactions {
  transactions(first: 10, orderBy: timestamp, orderDirection: desc) {
    id
    hash
    from {
      address
    }
    to {
      address
    }
    value
    timestamp
    block {
      number
    }
  }
}`}
            </pre>
            <p className="text-gray-600 dark:text-gray-400 mt-4 text-sm">
              This is just a preview. The actual GraphQL playground will be available soon.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
