'use client'
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Divider from '@/app/components/Divider';

export default function WalletPage() {
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-950 mt-28">
            {/* Header Section */}
            <div className="relative overflow-hidden">
                <div className="absolute inset-0 bg-gray-50 dark:bg-gray-950"></div>
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                    <div className="text-center">
                        <h1 className="text-4xl md:text-5xl font-bold text-gray-950 dark:text-white mb-6">
                            Create A Wallet
                        </h1>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                            Creating a cryptocurrency wallet is the first step to managing your assets and accessing blockchain networks like Etherchain AI.
                        </p>
                    </div>
                </div>
            </div>

            <Divider />

            {/* Main Content */}
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                {/* Warning Alert */}
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 mb-8">
                    <div className="flex items-start">
                        <div className="flex-shrink-0">
                            <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                                Remember, Never share your private keys or seed phrase with anyone!
                            </h3>
                            <p className="text-red-700 dark:text-red-300">
                                Your private keys and seed phrase are the only way to access your wallet. Keep them secure and never share them with anyone, including support staff.
                            </p>
                        </div>
                    </div>
                </div>

                {/* Which Wallet Section */}
                <div className="mb-12">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg className="w-8 h-8 mr-3 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                        Which Wallet Can I Use?
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                        You cannot use CEX wallets like Coinbase Wallet, or any centralized exchange wallet. You will need a self-custody wallet. We recommend using either MetaMask or TrustWallet.
                    </p>
                    
                    {/* Warning Box */}
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6">
                        <div className="flex items-start">
                            <div className="flex-shrink-0">
                                <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                            </div>
                            <div className="ml-3">
                                <p className="text-yellow-800 dark:text-yellow-200">
                                    <strong>NOTE:</strong> You cannot use any exchange wallet like Binance, Coinbase, Kraken, or any other centralized exchange. You will need to use a self-custody wallet like MetaMask or TrustWallet.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* MetaMask Section */}
                <div className="mb-12">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg className="w-8 h-8 mr-3 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                        </svg>
                        1. Create a Wallet with MetaMask (Desktop Browser)
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                        MetaMask is a browser extension wallet that allows you to securely manage Ethereum and other blockchain assets directly from your desktop.
                    </p>

                    <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 mb-8">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Steps to Create a MetaMask Wallet</h3>
                        
                        <div className="space-y-6">
                            <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mr-4 mt-1">
                                    <span className="text-purple-600 dark:text-purple-400 font-semibold text-sm">1</span>
                                </div>
                                <div className="">
                                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Install MetaMask</h4>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="text-purple-600 dark:text-purple-400 mr-2">•</span>
                                            <p className=''>
                                                Visit the <a href="https://metamask.io" target="_blank" rel="noopener noreferrer" className="text-purple-600 dark:text-purple-400 hover:underline">official MetaMask website</a> and download the extension for your preferred browser (Chrome, Firefox, Brave, or Edge).
                                            </p>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mr-4 mt-1">
                                    <span className="text-purple-600 dark:text-purple-400 font-semibold text-sm">2</span>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Set Up Your Wallet</h4>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="text-purple-600 dark:text-purple-400 mr-2">•</span>
                                            Open the MetaMask extension after installation.
                                        </li>
                                        <li className="flex items-start">
                                            <span className="text-purple-600 dark:text-purple-400 mr-2">•</span>
                                            <p>
                                                Click <strong>"Get Started"</strong> and choose <strong>"Create a Wallet."</strong>
                                            </p>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mr-4 mt-1">
                                    <span className="text-purple-600 dark:text-purple-400 font-semibold text-sm">3</span>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Secure Your Wallet</h4>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="text-purple-600 dark:text-purple-400 mr-2">•</span>
                                            Create a strong password and note it down securely.
                                        </li>
                                        <li className="flex items-start">
                                            <span className="text-purple-600 dark:text-purple-400 mr-2">•</span>
                                            <p>
                                                Back up your <strong>Secret Recovery Phrase</strong>. This phrase is the only way to recover your wallet if you lose access. Store it offline in a secure location.
                                            </p>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mr-4 mt-1">
                                    <span className="text-purple-600 dark:text-purple-400 font-semibold text-sm">4</span>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Start Using MetaMask</h4>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="text-purple-600 dark:text-purple-400 mr-2">•</span>
                                            Once set up, you can connect MetaMask to various dApps, buy Ethereum, and interact with blockchain platforms like Etherchain AI.
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Divider */}
                <div className="border-t border-gray-200 dark:border-gray-700 my-12"></div>

                {/* Trust Wallet Section */}
                <div className="mb-12">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg className="w-8 h-8 mr-3 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                        </svg>
                        2. Create a Wallet with Trust Wallet (Mobile)
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                        Trust Wallet is a mobile-first wallet designed for easy access to blockchain networks and assets on the go.
                    </p>

                    <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Steps to Create a Trust Wallet</h3>
                        
                        <div className="space-y-6">
                            <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-4 mt-1">
                                    <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Download Trust Wallet</h4>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="text-blue-600 dark:text-blue-400 mr-2">•</span>
                                            <p>
                                                Visit the <a href="https://trustwallet.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">official Trust Wallet website</a> or download it directly from the <strong>App Store (iOS)</strong> or <strong>Google Play Store (Android)</strong>.
                                            </p>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-4 mt-1">
                                    <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">2</span>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Set Up Your Wallet</h4>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="text-blue-600 dark:text-blue-400 mr-2">•</span>
                                            <p>
                                                Open the app and tap <strong>"Create a New Wallet."</strong>
                                            </p>
                                        </li>
                                        <li className="flex items-start">
                                            <span className="text-blue-600 dark:text-blue-400 mr-2">•</span>
                                            Agree to the Terms of Service.
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-4 mt-1">
                                    <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">3</span>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Secure Your Wallet</h4>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="text-blue-600 dark:text-blue-400 mr-2">•</span>
                                            <p>
                                                Back up your <strong>Recovery Phrase</strong> and store it securely offline. This phrase is essential for recovering your wallet.
                                            </p>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div className="flex items-start">
                                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-4 mt-1">
                                    <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">4</span>
                                </div>
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Start Using Trust Wallet</h4>
                                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                                        <li className="flex items-start">
                                            <span className="text-blue-600 dark:text-blue-400 mr-2">•</span>
                                            Add Ethereum or other assets to your wallet by purchasing within the app or transferring from an exchange.
                                        </li>
                                        <li className="flex items-start">
                                            <span className="text-blue-600 dark:text-blue-400 mr-2">•</span>
                                            Use the wallet to interact with blockchain networks and dApps, including Etherchain AI.
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Navigation */}
                <div className="flex flex-col md:flex-row gap-4 mt-12">
                    <Link 
                        href="/how-to-buy" 
                        className="flex-1 group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-purple-500 dark:hover:border-purple-400 transition-all duration-300"
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <svg className="w-5 h-5 text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"/>
                                </svg>
                                <div className="ml-3">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">Previous</p>
                                    <p className="font-medium text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">How to Buy</p>
                                </div>
                            </div>
                        </div>
                    </Link>
                    
                    <Link 
                        href="/how-to-buy/get-ethereum" 
                        className="flex-1 group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-purple-500 dark:hover:border-purple-400 transition-all duration-300"
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <div className="mr-3">
                                    <p className="text-sm text-gray-500 dark:text-gray-400">Next</p>
                                    <p className="font-medium text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">Get Ethereum</p>
                                </div>
                            </div>
                            <svg className="w-5 h-5 text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </div>
                    </Link>
                </div>

                {/* Last Updated */}
                <div className="mt-8 text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                        Last updated <time>December 2024</time>
                    </p>
                </div>
            </div>
        </div>
    );
}
