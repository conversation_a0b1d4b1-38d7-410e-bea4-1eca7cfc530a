'use client';

import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faExchangeAlt,
  faWallet,
  faCoins,
  faArrowsUpDown,
  faCheckCircle,
  faExclamationTriangle,
  faSpinner,
  faAngleDown,
  faPencilRuler 
} from '@fortawesome/free-solid-svg-icons';

export default function SwapPage() {
  const [fromToken, setFromToken] = useState('ETH');
  const [toToken, setToToken] = useState('ETHAI');
  const [fromAmount, setFromAmount] = useState('');
  const [toAmount, setToAmount] = useState('');
  const [slippage, setSlippage] = useState(0.5);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');
  const [isWalletConnected, setIsWalletConnected] = useState(false);

  const tokens = [
    { symbol: 'ETH', name: 'Ethereum', icon: '🔵', balance: '0.0' },
    { symbol: 'ETHAI', name: 'Etherchain AI', icon: '🟣', balance: '0.0' },
    { symbol: 'USDC', name: 'USD Coin', icon: '🔵', balance: '0.0' },
    { symbol: 'USDT', name: 'Tether', icon: '🟢', balance: '0.0' },
    { symbol: 'WBTC', name: 'Wrapped Bitcoin', icon: '🟠', balance: '0.0' }
  ];

  const handleSwapTokens = () => {
    const tempToken = fromToken;
    const tempAmount = fromAmount;
    setFromToken(toToken);
    setToToken(tempToken);
    setFromAmount(toAmount);
    setToAmount(tempAmount);
  };

  const handleFromAmountChange = (value) => {
    setFromAmount(value);
    // Simulate price calculation
    if (value && parseFloat(value) > 0) {
      const calculatedAmount = parseFloat(value) * 1.5; // Mock exchange rate
      setToAmount(calculatedAmount.toFixed(6));
    } else {
      setToAmount('');
    }
  };

  const handleConnectWallet = () => {
    setIsWalletConnected(true);
    // Simulate wallet connection
    setTimeout(() => {
      // Update balances
      const updatedTokens = tokens.map(token => ({
        ...token,
        balance: (Math.random() * 10).toFixed(4)
      }));
      // In a real app, you would update the state with actual balances
    }, 1000);
  };

  const handleSwap = async (e) => {
    e.preventDefault();
    
    if (!isWalletConnected) {
      setError('Please connect your wallet first');
      return;
    }

    if (!fromAmount || parseFloat(fromAmount) <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    setIsLoading(true);
    setError('');

    // Simulate swap transaction
    setTimeout(() => {
      setIsLoading(false);
      setIsSuccess(true);
      setFromAmount('');
      setToAmount('');
      
      // Reset success message after 5 seconds
      setTimeout(() => {
        setIsSuccess(false);
      }, 5000);
    }, 2000);
  };

  const getTokenInfo = (symbol) => {
    return tokens.find(token => token.symbol === symbol);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <FontAwesomeIcon icon={faExchangeAlt} className="mr-3 text-blue-600 dark:text-blue-400" />
                Token Swap
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Swap tokens instantly on Etherchain with the best rates
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container py-12 max-w-lg mx-auto">
        <div className="bg-white dark:bg-gray-900/50 border-2 border-gray-200 dark:border-gray-700 rounded-xl shadow-lg overflow-hidden">
          {/* Card Header */}
          <div className="px-6 py-6 bg-gray-50 dark:bg-gray-900/50 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-center text-gray-900 dark:text-white">
              Swap <span className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">ETH</span> Anywhere
            </h2>
          </div>

          {/* Card Content */}
          <div className="p-4">
            <div className="space-y-4">
              {/* You Pay Section */}
              <div className="space-y-2 pt-4 px-4 pb-4 rounded-lg bg-gray-50 dark:bg-gray-900/50 border-2 border-blue-200 dark:border-blue-500/20">
                <div className="flex flex-wrap items-center justify-between gap-4">
                  <span className="text-sm text-gray-700 dark:text-gray-300 font-semibold">You Pay</span>
                  <button className="inline-flex items-center font-medium transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 px-4 py-2 text-sm gap-2 justify-between border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-transparent text-gray-900 dark:text-white">
                    <span className="text-sm">Select a token</span>
                    <FontAwesomeIcon icon={faAngleDown} />
                  </button>
                </div>
                <input
                  type="number"
                  min="0"
                  placeholder="0.0"
                  value={fromAmount}
                  onChange={(e) => handleFromAmountChange(e.target.value)}
                  className="w-full px-2 py-3 text-2xl outline-none text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-500 border-b border-blue-200 dark:border-blue-500/20 bg-transparent"
                />
                <div className="mt-4 flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Balance: {getTokenInfo(fromToken)?.balance} {fromToken}</span>
                </div>
              </div>

              {/* Swap Arrow */}
              <div className="relative py-2">
                <div className="absolute top-1/2 left-0 right-0 -translate-y-1/2 flex items-center justify-center bg-gray-50 dark:bg-gray-900 size-15 mx-auto rounded-lg">
                  <button
                    onClick={handleSwapTokens}
                    className="size-12 bg-white dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                  >
                    <FontAwesomeIcon icon={faArrowsUpDown} className="text-blue-600 dark:text-blue-400" />
                  </button>
                </div>
              </div>

              {/* You Get Section */}
              <div className="space-y-2 pt-4 px-4 pb-4 rounded-lg bg-gray-50 dark:bg-gray-900/50 border-2 border-blue-200 dark:border-blue-500/20">
                <div className="flex flex-wrap items-center justify-between gap-4">
                  <span className="text-sm text-gray-700 dark:text-gray-300 font-semibold">You Get</span>
                  <button className="inline-flex items-center font-medium transition duration-300 hover:bg-gray-200 dark:hover:bg-gray-700 px-4 py-2 text-sm gap-2 justify-between border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-transparent text-gray-900 dark:text-white">
                    <span className="text-sm">Select a token</span>
                    <FontAwesomeIcon icon={faAngleDown} />
                  </button>
                </div>
                <input
                  type="number"
                  placeholder="0.0"
                  value={toAmount}
                  readOnly
                  className="w-full px-2 py-3 text-2xl outline-none text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-500 border-b border-blue-200 dark:border-blue-500/20 bg-transparent"
                />
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    <button className="flex items-center gap-2 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                      <FontAwesomeIcon icon={faPencilRuler} />
                      <span>{slippage}%</span>
                    </button>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Balance: {getTokenInfo(toToken)?.balance} {toToken}</span>
                </div>
              </div>

              {/* Connect Wallet Button */}
              {!isWalletConnected ? (
                <button
                  onClick={handleConnectWallet}
                  className="w-full py-4 px-6 rounded-lg font-semibold transition-colors bg-blue-600 text-white hover:bg-blue-700 h-12"
                >
                  <FontAwesomeIcon icon={faWallet} className="mr-2" />
                  Connect Wallet
                </button>
              ) : (
                <>
                  {/* Error and Success Messages */}
                  {error && (
                    <div className="flex items-center text-red-600 dark:text-red-400 text-sm">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                      {error}
                    </div>
                  )}

                  {isSuccess && (
                    <div className="flex items-center text-green-600 dark:text-green-400 text-sm">
                      <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
                      Swap completed successfully!
                    </div>
                  )}

                  {/* Swap Button */}
                  <button
                    onClick={handleSwap}
                    disabled={isLoading || !fromAmount || parseFloat(fromAmount) <= 0}
                    className={`w-full py-4 px-6 rounded-lg font-semibold transition-colors h-12 ${
                      isLoading || !fromAmount || parseFloat(fromAmount) <= 0
                        ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                        Swapping...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <FontAwesomeIcon icon={faExchangeAlt} className="mr-2" />
                        Swap Tokens
                      </div>
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Swap Info */}
        <div className="mt-8 grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">How to Swap</h3>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li>• Connect your wallet</li>
              <li>• Select tokens to swap</li>
              <li>• Enter amount</li>
              <li>• Review and confirm</li>
              <li>• Wait for transaction</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Supported Tokens</h3>
            <div className="grid grid-cols-2 gap-2">
              {tokens.map(token => (
                <div key={token.symbol} className="flex items-center text-gray-600 dark:text-gray-400">
                  <span className="mr-2">{token.icon}</span>
                  <span className="text-sm">{token.symbol}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
