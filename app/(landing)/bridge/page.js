'use client';

import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faExchangeAlt,
  faWallet,
  faNetworkWired,
  faCoins,
  faArrowRight,
  faCheckCircle,
  faExclamationTriangle,
  faSpinner,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';

export default function BridgePage() {
  const [fromNetwork, setFromNetwork] = useState('ethereum');
  const [toNetwork, setToNetwork] = useState('etherchain');
  const [amount, setAmount] = useState('');
  const [recipientAddress, setRecipientAddress] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');

  const networks = [
    { id: 'ethereum', name: 'Ethereum', icon: '🔵', chainId: 1 },
    { id: 'etherchain', name: 'Etherchain', icon: '🟣', chainId: 1337 },
    { id: 'polygon', name: 'Polygon', icon: '🟣', chainId: 137 },
    { id: 'bsc', name: 'BSC', icon: '🟡', chainId: 56 }
  ];

  const handleSwapNetworks = () => {
    setFromNetwork(toNetwork);
    setToNetwork(fromNetwork);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!amount || !recipientAddress) {
      setError('Please fill in all fields');
      return;
    }

    if (parseFloat(amount) <= 0) {
      setError('Amount must be greater than 0');
      return;
    }

    if (!recipientAddress.startsWith('0x') || recipientAddress.length !== 42) {
      setError('Please enter a valid Ethereum address');
      return;
    }

    setIsLoading(true);
    setError('');

    // Simulate bridge transaction
    setTimeout(() => {
      setIsLoading(false);
      setIsSuccess(true);
      setAmount('');
      setRecipientAddress('');
      
      // Reset success message after 5 seconds
      setTimeout(() => {
        setIsSuccess(false);
      }, 5000);
    }, 3000);
  };

  const getNetworkInfo = (networkId) => {
    return networks.find(network => network.id === networkId);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <FontAwesomeIcon icon={faExchangeAlt} className="mr-3 text-blue-600 dark:text-blue-400" />
                Token Bridge
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Transfer tokens between Ethereum and Etherchain networks securely
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Bridge Form */}
        <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* From Network */}
            <div className="space-y-3">
              <label className="text-sm font-medium flex items-center text-gray-900 dark:text-white">
                <FontAwesomeIcon icon={faNetworkWired} className="mr-2 text-gray-500 dark:text-gray-400" />
                From Network
              </label>
              <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{getNetworkInfo(fromNetwork)?.icon}</span>
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">{getNetworkInfo(fromNetwork)?.name}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Chain ID: {getNetworkInfo(fromNetwork)?.chainId}</div>
                    </div>
                  </div>
                  <select
                    value={fromNetwork}
                    onChange={(e) => setFromNetwork(e.target.value)}
                    className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500"
                  >
                    {networks.map(network => (
                      <option key={network.id} value={network.id}>
                        {network.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Swap Button */}
            <div className="flex justify-center">
              <button
                type="button"
                onClick={handleSwapNetworks}
                className="bg-gray-100 dark:bg-gray-900 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-full p-3 transition-colors"
              >
                <FontAwesomeIcon icon={faArrowRight} className="text-blue-600 dark:text-blue-400" />
              </button>
            </div>

            {/* To Network */}
            <div className="space-y-3">
              <label className="text-sm font-medium flex items-center text-gray-900 dark:text-white">
                <FontAwesomeIcon icon={faNetworkWired} className="mr-2 text-gray-500 dark:text-gray-400" />
                To Network
              </label>
              <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{getNetworkInfo(toNetwork)?.icon}</span>
                    <div>
                      <div className="font-semibold text-gray-900 dark:text-white">{getNetworkInfo(toNetwork)?.name}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Chain ID: {getNetworkInfo(toNetwork)?.chainId}</div>
                    </div>
                  </div>
                  <select
                    value={toNetwork}
                    onChange={(e) => setToNetwork(e.target.value)}
                    className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500"
                  >
                    {networks.map(network => (
                      <option key={network.id} value={network.id}>
                        {network.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Amount */}
            <div className="space-y-3">
              <label className="text-sm font-medium flex items-center text-gray-900 dark:text-white">
                <FontAwesomeIcon icon={faCoins} className="mr-2 text-gray-500 dark:text-gray-400" />
                Amount
              </label>
              <div className="relative">
                <input
                  type="number"
                  placeholder="0.0"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  step="0.000001"
                  min="0"
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
                  ETH
                </div>
              </div>
            </div>

            {/* Recipient Address */}
            <div className="space-y-3">
              <label className="text-sm font-medium flex items-center text-gray-900 dark:text-white">
                <FontAwesomeIcon icon={faWallet} className="mr-2 text-gray-500 dark:text-gray-400" />
                Recipient Address
              </label>
              <input
                type="text"
                placeholder="0x..."
                value={recipientAddress}
                onChange={(e) => setRecipientAddress(e.target.value)}
                className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              />
            </div>

            {/* Error and Success Messages */}
            {error && (
              <div className="flex items-center text-red-600 dark:text-red-400 text-sm">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                {error}
              </div>
            )}

            {isSuccess && (
              <div className="flex items-center text-green-600 dark:text-green-400 text-sm">
                <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
                Bridge transaction initiated successfully! Your tokens will be transferred shortly.
              </div>
            )}

            {/* Bridge Button */}
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-4 px-6 rounded-lg font-semibold transition-colors ${
                isLoading
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" />
                  Processing Bridge...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <FontAwesomeIcon icon={faExchangeAlt} className="mr-2" />
                  Bridge Tokens
                </div>
              )}
            </button>
          </form>
        </div>

        {/* Bridge Info */}
        <div className="mt-8 grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">How Bridge Works</h3>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li>• Select source and destination networks</li>
              <li>• Enter amount and recipient address</li>
              <li>• Confirm transaction on source network</li>
              <li>• Wait for bridge confirmation</li>
              <li>• Receive tokens on destination network</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Bridge Fees</h3>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li>• Network gas fees on source chain</li>
              <li>• Bridge processing fee: 0.1%</li>
              <li>• Network gas fees on destination chain</li>
              <li>• Estimated time: 5-15 minutes</li>
            </ul>
          </div>
        </div>

        {/* Supported Networks */}
        <div className="mt-8">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Supported Networks</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            {networks.map(network => (
              <div key={network.id} className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                <div className="flex items-center">
                  <span className="text-2xl mr-3">{network.icon}</span>
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">{network.name}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Chain ID: {network.chainId}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
