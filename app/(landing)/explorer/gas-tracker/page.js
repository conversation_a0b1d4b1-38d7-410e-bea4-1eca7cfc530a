'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faSearch, 
  faGasPump, 
  faExchangeAlt, 
  faCube, 
  faUsers, 
  faShieldAlt, 
  faArrowUp,
  faToken,
  faArrowsAltH,
  faCode,
  faExternalLinkAlt,
  faFileContract,
  faTwitter,
  faTelegram,
  faLink,
  faClock,
  faCheckCircle,
  faTimesCircle,
  faSpinner,
  faCoins,
  faChartLine,
  faSort,
  faSortUp,
  faSortDown,
  faCopy,
  faEye,
  faCalculator,
  faDollarSign,
  faGaugeHigh,
  faGaugeSimpleMed
} from '@fortawesome/free-solid-svg-icons';
import { faTwitter as fabTwitter, faTelegram as fabTelegram } from '@fortawesome/free-brands-svg-icons';
import { useGasData } from '@/hooks/useGasData';
import { ethers } from 'ethers';
import BigNumber from 'bignumber.js';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default function GasTrackerPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [currentGasPrices, setCurrentGasPrices] = useState({
    slow: { gwei: 0, usd: 0, time: '~5 min' },
    standard: { gwei: 0, usd: 0, time: '~2 min' },
    fast: { gwei: 0, usd: 0, time: '~30 sec' },
    instant: { gwei: 0, usd: 0, time: '~15 sec' }
  });
  const [gasHistory, setGasHistory] = useState([]);
  const [chartData, setChartData] = useState({
    labels: [],
    datasets: []
  });
  const [lastUpdate, setLastUpdate] = useState(null);
  
  // Use the gas data hook
  const { data: gasData, loading, error } = useGasData({
    rpcUrl: "https://eth.llamarpc.com",
    blockCount: 30,
    percentiles: [10, 50, 90]
  });

  // Update data when gasData changes
  useEffect(() => {
    if(loading) {
      return;
    }

    if (!gasData || !gasData.baseFeePerGas || gasData.baseFeePerGas.length === 0) {
      return;
    }

    // Calculate current gas prices
    const latestBaseFee = gasData.baseFeePerGas[gasData.baseFeePerGas.length - 1];
    const latestRewards = gasData.reward[gasData.reward.length - 1];
    
    // Convert from wei to gwei
    const baseFeeGwei = parseFloat(new BigNumber(latestBaseFee).div(1000000000).toString());
    const slowGwei = baseFeeGwei + parseFloat(new BigNumber(latestRewards[0]).div(1000000000).toString()); // 10th percentile
    const standardGwei = baseFeeGwei + parseFloat(new BigNumber(latestRewards[1]).div(1000000000).toString()); // 50th percentile
    const fastGwei = baseFeeGwei + parseFloat(new BigNumber(latestRewards[2]).div(1000000000).toString()); // 90th percentile
    const instantGwei = fastGwei * 1.5; // Estimate for instant

    // Estimate USD values (assuming ETH price of $3000)
    const ethPrice = 3000;
    const gasLimit = 21000; // Standard ETH transfer
    const slowUsd = (slowGwei * gasLimit * ethPrice) / 1000000000;
    const standardUsd = (standardGwei * gasLimit * ethPrice) / 1000000000;
    const fastUsd = (fastGwei * gasLimit * ethPrice) / 1000000000;
    const instantUsd = (instantGwei * gasLimit * ethPrice) / 1000000000;

    setCurrentGasPrices({
      slow: { gwei: Math.round(slowGwei), usd: slowUsd, time: '~5 min' },
      standard: { gwei: Math.round(standardGwei), usd: standardUsd, time: '~2 min' },
      fast: { gwei: Math.round(fastGwei), usd: fastUsd, time: '~30 sec' },
      instant: { gwei: Math.round(instantGwei), usd: instantUsd, time: '~15 sec' }
    });

    // Generate gas history
    const history = gasData.baseFeePerGas.slice(-10).map((baseFee, index) => {
      const rewards = gasData.reward[gasData.reward.length - 10 + index];
      const baseFeeGwei = parseFloat(new BigNumber(baseFee).div(1000000000).toString());
      const slowGwei = baseFeeGwei + parseFloat(new BigNumber(rewards[0]).div(1000000000).toString());
      const standardGwei = baseFeeGwei + parseFloat(new BigNumber(rewards[1]).div(1000000000).toString());
      const fastGwei = baseFeeGwei + parseFloat(new BigNumber(rewards[2]).div(1000000000).toString());
      
      // Calculate time (assuming 12 second block time)
      const minutesAgo = Math.round((gasData.baseFeePerGas.length - 10 + index) * 12 / 60);
      
      return {
        time: `${minutesAgo} min ago`,
        slow: Math.round(slowGwei),
        standard: Math.round(standardGwei),
        fast: Math.round(fastGwei),
        baseFee: Math.round(baseFeeGwei)
      };
    }).reverse();

    setGasHistory(history);

    // Generate chart data
    const labels = gasData.baseFeePerGas.map((_, index) => {
      const minutesAgo = Math.round((gasData.baseFeePerGas.length - index - 1) * 12 / 60);
      return `${minutesAgo}m ago`;
    }).reverse();

    const baseFeeData = gasData.baseFeePerGas.map(baseFee => 
      parseFloat(new BigNumber(baseFee).div(1000000000).toString())
    ).reverse();

    const slowData = gasData.baseFeePerGas.map((baseFee, index) => {
      if(index > 29) {
        return 0;
      }

      const rewards = gasData.reward[index];
      const baseFeeGwei = parseFloat(new BigNumber(baseFee).div(1000000000).toString());
      const slowGwei = baseFeeGwei + parseFloat(new BigNumber(rewards[0]).div(1000000000).toString());
      return slowGwei;
    }).reverse();

    const standardData = gasData.baseFeePerGas.map((baseFee, index) => {
      if(index > 29) {
        return 0;
      }

      const rewards = gasData.reward[index];
      const baseFeeGwei = parseFloat(new BigNumber(baseFee).div(1000000000).toString());
      const standardGwei = baseFeeGwei + parseFloat(new BigNumber(rewards[1]).div(1000000000).toString());
      return standardGwei;
    }).reverse();

    const fastData = gasData.baseFeePerGas.map((baseFee, index) => {
      if(index > 29) {
        return 0;
      }

      const rewards = gasData.reward[index];
      const baseFeeGwei = parseFloat(new BigNumber(baseFee).div(1000000000).toString());
      const fastGwei = baseFeeGwei + parseFloat(new BigNumber(rewards[2]).div(1000000000).toString());
      return fastGwei;
    }).reverse();

    setChartData({
      labels,
      datasets: [
        {
          label: 'Base Fee',
          data: baseFeeData,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5
        },
        {
          label: 'Slow',
          data: slowData,
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5
        },
        {
          label: 'Standard',
          data: standardData,
          borderColor: 'rgb(234, 179, 8)',
          backgroundColor: 'rgba(234, 179, 8, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5
        },
        {
          label: 'Fast',
          data: fastData,
          borderColor: 'rgb(249, 115, 22)',
          backgroundColor: 'rgba(249, 115, 22, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          pointRadius: 3,
          pointHoverRadius: 5
        }
      ]
    });

    // Update last update timestamp
    setLastUpdate(new Date());

  }, [gasData]);

  // Update chart colors based on theme
  const isDarkMode = typeof window !== 'undefined' && document.documentElement.classList.contains('dark');

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          color: isDarkMode ? '#f3f4f6' : '#374151'
        }
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
        titleColor: isDarkMode ? '#f3f4f6' : '#374151',
        bodyColor: isDarkMode ? '#f3f4f6' : '#374151',
        borderColor: isDarkMode ? '#374151' : '#d1d5db',
        borderWidth: 1,
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(1)} Gwei`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          color: isDarkMode ? '#374151' : '#e5e7eb'
        },
        ticks: {
          color: isDarkMode ? '#9ca3af' : '#6b7280',
          maxTicksLimit: 8
        }
      },
      y: {
        grid: {
          color: isDarkMode ? '#374151' : '#e5e7eb'
        },
        ticks: {
          color: isDarkMode ? '#9ca3af' : '#6b7280',
          callback: function(value) {
            return `${value} Gwei`;
          }
        }
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    }
  };
  const formatUSD = (usd) => {
    return `$${usd.toFixed(2)}`;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
              Gas Tracker
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
              Monitor gas prices and optimize your transactions on Etherchain
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Current Gas Prices */}
        {loading ? (
          <div className="flex justify-center items-center py-12 mb-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
            <span className="ml-3 text-gray-600 dark:text-gray-400">Loading gas data...</span>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center py-12 mb-8">
            <div className="text-center">
              <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 text-red-600 dark:text-red-400 mb-4" />
              <p className="text-red-600 dark:text-red-400">Error loading gas data. Please try again later.</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Slow</h3>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">{currentGasPrices.slow.gwei} Gwei</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">~{formatUSD(currentGasPrices.slow.usd)}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{currentGasPrices.slow.time}</p>
                </div>
                <div className="bg-green-100 dark:bg-green-900/20 p-3 rounded-lg">
                  <FontAwesomeIcon icon={faGasPump} className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Standard</h3>
                  <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{currentGasPrices.standard.gwei} Gwei</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">~{formatUSD(currentGasPrices.standard.usd)}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{currentGasPrices.standard.time}</p>
                </div>
                <div className="bg-yellow-100 dark:bg-yellow-900/20 p-3 rounded-lg">
                  <FontAwesomeIcon icon={faGasPump} className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Fast</h3>
                  <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{currentGasPrices.fast.gwei} Gwei</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">~{formatUSD(currentGasPrices.fast.usd)}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{currentGasPrices.fast.time}</p>
                </div>
                <div className="bg-orange-100 dark:bg-orange-900/20 p-3 rounded-lg">
                  <FontAwesomeIcon icon={faGasPump} className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Instant</h3>
                  <p className="text-2xl font-bold text-red-600 dark:text-red-400">{currentGasPrices.instant.gwei} Gwei</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">~{formatUSD(currentGasPrices.instant.usd)}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{currentGasPrices.instant.time}</p>
                </div>
                <div className="bg-red-100 dark:bg-red-900/20 p-3 rounded-lg">
                  <FontAwesomeIcon icon={faGasPump} className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Gas Stats */}
        {!loading && !error && gasData && (
          <div className="mb-4 flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Live Data
              </span>
            </div>
            {lastUpdate && (
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Last updated: {lastUpdate.toLocaleTimeString()}
              </span>
            )}
          </div>
        )}

        {!loading && !error && gasData && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FontAwesomeIcon icon={faGaugeHigh} className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Base Fee</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {gasData.baseFeePerGas && gasData.baseFeePerGas.length > 0 
                      ? `${Math.round(parseFloat(new BigNumber(gasData.baseFeePerGas[gasData.baseFeePerGas.length - 1]).div(1000000000).toString()))} Gwei`
                      : 'N/A'
                    }
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FontAwesomeIcon icon={faCube} className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Blocks Analyzed</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {gasData.baseFeePerGas ? gasData.baseFeePerGas.length : 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FontAwesomeIcon icon={faCalculator} className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Gas Used</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {gasData.gasUsedRatio 
                      ? `${(gasData.gasUsedRatio.reduce((sum, ratio) => sum + ratio, 0) / gasData.gasUsedRatio.length * 100).toFixed(1)}%`
                      : 'N/A'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Gas Price Chart */}
        <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Gas Price History (Last 30 Blocks)</h2>
          {loading ? (
            <div className="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading chart data...</p>
              </div>
            </div>
          ) : error ? (
            <div className="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <FontAwesomeIcon icon={faSpinner} className="h-12 w-12 text-red-600 dark:text-red-400 mb-4" />
                <p className="text-red-600 dark:text-red-400">Error loading chart data.</p>
              </div>
            </div>
          ) : chartData.labels.length === 0 ? (
            <div className="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <FontAwesomeIcon icon={faChartLine} className="h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-600 dark:text-gray-400">No chart data available.</p>
              </div>
            </div>
          ) : (
            <div className="h-64">
              <Line data={chartData} options={chartOptions} />
            </div>
          )}
        </div>

        {/* Recent Gas Prices */}
        <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Gas Prices</h2>
          </div>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading gas history...</span>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center py-12">
              <div className="text-center">
                <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 text-red-600 dark:text-red-400 mb-4" />
                <p className="text-red-600 dark:text-red-400">Error loading gas history.</p>
              </div>
            </div>
          ) : gasHistory.length === 0 ? (
            <div className="flex justify-center items-center py-12">
              <div className="text-center">
                <FontAwesomeIcon icon={faGasPump} className="h-8 w-8 text-gray-400 mb-4" />
                <p className="text-gray-600 dark:text-gray-400">No gas history available.</p>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Slow
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Standard
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Fast
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Base Fee
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                  {gasHistory.map((entry, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {entry.time}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">
                        {entry.slow} Gwei
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600 dark:text-yellow-400">
                        {entry.standard} Gwei
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-orange-600 dark:text-orange-400">
                        {entry.fast} Gwei
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {entry.baseFee} Gwei
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

