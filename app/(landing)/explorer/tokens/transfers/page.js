'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faSearch, 
  faArrowsAltH, 
  faExchangeAlt, 
  faCube, 
  faUsers, 
  faShieldAlt, 
  faArrowUp,
  faCoins,
  faCode,
  faExternalLinkAlt,
  faFileContract,
  faGasPump,
  faTwitter,
  faTelegram,
  faLink,
  faSort,
  faSortUp,
  faSortDown,
  faCopy,
  faEye,
  faClock,
  faCheckCircle,
  faTimesCircle,
  faSpinner,
  faArrowRight,
  faWallet
} from '@fortawesome/free-solid-svg-icons';
import { faTwitter as fabTwitter, faTelegram as fabTelegram } from '@fortawesome/free-brands-svg-icons';

export default function TokenTransfersPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [tokenTransfers, setTokenTransfers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortField, setSortField] = useState('timestamp');
  const [sortDirection, setSortDirection] = useState('desc');

  // Mock data for token transfers
  useEffect(() => {
    const mockTransfers = [
      {
        id: 1,
        txHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        tokenName: 'Etherchain Token',
        tokenSymbol: 'wETHAI',
        from: '******************************************',
        to: '******************************************',
        amount: '1,000,000',
        value: '$2,500.00',
        timestamp: '2 minutes ago',
        block: 12345678,
        status: 'success'
      },
      {
        id: 2,
        txHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
        tokenName: 'Solana AI',
        tokenSymbol: 'SAI',
        from: '******************************************',
        to: '******************************************',
        amount: '500,000',
        value: '$1,250.00',
        timestamp: '5 minutes ago',
        block: 12345677,
        status: 'success'
      },
      {
        id: 3,
        txHash: '0x567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234',
        tokenName: 'Test Token',
        tokenSymbol: 'TEST',
        from: '******************************************',
        to: '0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b1',
        amount: '100,000',
        value: '$250.00',
        timestamp: '10 minutes ago',
        block: 12345676,
        status: 'success'
      }
    ];

    setTimeout(() => {
      setTokenTransfers(mockTransfers);
      setIsLoading(false);
    }, 1000);
  }, []);

  const formatHash = (hash) => {
    return `${hash.slice(0, 6)}...${hash.slice(-4)}`;
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const filteredTransfers = tokenTransfers.filter(transfer =>
    transfer.tokenName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    transfer.tokenSymbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
    transfer.txHash.toLowerCase().includes(searchQuery.toLowerCase()) ||
    transfer.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
    transfer.to.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedTransfers = [...filteredTransfers].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];
    
    if (sortField === 'amount') {
      aValue = parseFloat(aValue.replace(/,/g, ''));
      bValue = parseFloat(bValue.replace(/,/g, ''));
    } else if (sortField === 'value') {
      aValue = parseFloat(aValue.replace(/[$,]/g, ''));
      bValue = parseFloat(bValue.replace(/[$,]/g, ''));
    }
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faArrowsAltH} className="h-8 w-8 text-blue-400" />
              </div>
              <div className="ml-5 flex-1 w-full">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Token Transfers</h1>
                <p className="text-gray-600 dark:text-gray-400">Track all token transfers on Etherchain</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FontAwesomeIcon icon={faSearch} className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md leading-5 bg-white dark:bg-gray-900 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:focus:placeholder-gray-500 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search by token name, symbol, transaction hash, or address..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faArrowsAltH} className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Transfers</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{tokenTransfers.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faCoins} className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Unique Tokens</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">3</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faUsers} className="h-6 w-6 text-purple-400" />
              </div>
              <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Addresses</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">12</p>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faClock} className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Last 24h</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">156</p>
              </div>
            </div>
          </div>
        </div>

        {/* Token Transfers Table */}
        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Recent Token Transfers</h3>
          </div>
          
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 text-blue-400 animate-spin" />
              <span className="ml-3 text-gray-400">Loading token transfers...</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-850 uppercase tracking-wider cursor-pointer hover:text-white" onClick={() => handleSort('txHash')}>
                      <div className="flex items-center">
                        Transaction Hash
                        <FontAwesomeIcon 
                          icon={sortField === 'txHash' ? (sortDirection === 'asc' ? faSortUp : faSortDown) : faSort} 
                          className="ml-2 h-3 w-3" 
                        />
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-850 uppercase tracking-wider cursor-pointer hover:text-white" onClick={() => handleSort('tokenName')}>
                      <div className="flex items-center">
                        Token
                        <FontAwesomeIcon 
                          icon={sortField === 'tokenName' ? (sortDirection === 'asc' ? faSortUp : faSortDown) : faSort} 
                          className="ml-2 h-3 w-3" 
                        />
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-850 uppercase tracking-wider">
                      From
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-850 uppercase tracking-wider">
                      To
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-850 uppercase tracking-wider cursor-pointer hover:text-white" onClick={() => handleSort('amount')}>
                      <div className="flex items-center">
                        Amount
                        <FontAwesomeIcon 
                          icon={sortField === 'amount' ? (sortDirection === 'asc' ? faSortUp : faSortDown) : faSort} 
                          className="ml-2 h-3 w-3" 
                        />
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-850 uppercase tracking-wider cursor-pointer hover:text-white" onClick={() => handleSort('value')}>
                      <div className="flex items-center">
                        Value
                        <FontAwesomeIcon 
                          icon={sortField === 'value' ? (sortDirection === 'asc' ? faSortUp : faSortDown) : faSort} 
                          className="ml-2 h-3 w-3" 
                        />
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-850 uppercase tracking-wider cursor-pointer hover:text-white" onClick={() => handleSort('timestamp')}>
                      <div className="flex items-center">
                        Time
                        <FontAwesomeIcon 
                          icon={sortField === 'timestamp' ? (sortDirection === 'asc' ? faSortUp : faSortDown) : faSort} 
                          className="ml-2 h-3 w-3" 
                        />
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-850 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-50 dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                  {sortedTransfers.length > 0 ? (
                    sortedTransfers.map((transfer) => (
                      <tr key={transfer.id} className="hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-blue-400 font-mono">
                              {formatHash(transfer.txHash)}
                            </span>
                            <button
                              onClick={() => copyToClipboard(transfer.txHash)}
                              className="text-gray-850 dark:text-gray-200 hover:text-white dark:hover:text-white transition-colors"
                            >
                              <FontAwesomeIcon icon={faCopy} className="text-xs" />
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-8 w-8">
                              <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                <FontAwesomeIcon icon={faCoins} className="h-4 w-4 text-white" />
                              </div>
                            </div>
                            <div className="ml-3">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">{transfer.tokenName}</div>
                              <div className="text-sm text-gray-600 dark:text-gray-400">{transfer.tokenSymbol}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-850 dark:text-gray-200 font-mono">
                              {formatHash(transfer.from)}
                            </span>
                            <button
                              onClick={() => copyToClipboard(transfer.from)}
                              className="text-gray-850 dark:text-gray-200 hover:text-white dark:hover:text-white transition-colors"
                            >
                              <FontAwesomeIcon icon={faCopy} className="text-xs" />
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <FontAwesomeIcon icon={faArrowRight} className="text-gray-400 text-xs" />
                            <span className="text-sm text-gray-850 dark:text-gray-200 font-mono">
                              {formatHash(transfer.to)}
                            </span>
                            <button
                              onClick={() => copyToClipboard(transfer.to)}
                              className="text-gray-850 dark:text-gray-200 hover:text-white dark:hover:text-white transition-colors"
                            >
                              <FontAwesomeIcon icon={faCopy} className="text-xs" />
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-850 dark:text-gray-200">{transfer.amount}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-850 dark:text-gray-200">
                          {transfer.timestamp}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                            <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />
                            Success
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button className="text-blue-400 dark:text-blue-300 hover:text-blue-300 dark:hover:text-blue-200 transition-colors">
                              <FontAwesomeIcon icon={faEye} className="text-sm" />
                            </button>
                            <button className="text-gray-400 dark:text-gray-300 hover:text-white dark:hover:text-white transition-colors">
                              <FontAwesomeIcon icon={faExternalLinkAlt} className="text-sm" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="9" className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center">
                          <FontAwesomeIcon icon={faArrowsAltH} className="h-12 w-12 text-gray-500 mb-4" />
                          <h3 className="text-lg font-medium text-gray-300 mb-2">No token transfers found</h3>
                          <p className="text-gray-500">Token transfers will appear here once they are processed</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Quick Links */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <a href="/tokens" className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 hover:border-blue-500 transition-colors group">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faCoins} className="h-8 w-8 text-blue-400 group-hover:text-blue-300" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white group-hover:text-blue-300">Tokens</h3>
                <p className="text-gray-600 dark:text-gray-400">Browse all tokens</p>
              </div>
            </div>
          </a>
          
          <a href="/txs" className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 hover:border-blue-500 transition-colors group">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faExchangeAlt} className="h-8 w-8 text-green-400 group-hover:text-green-300" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white group-hover:text-green-300">Transactions</h3>
                <p className="text-gray-600 dark:text-gray-400">View all transactions</p>
              </div>
            </div>
          </a>
          
          <a href="/blocks" className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 hover:border-blue-500 transition-colors group">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faCube} className="h-8 w-8 text-purple-400 group-hover:text-purple-300" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white group-hover:text-purple-300">Blocks</h3>
                <p className="text-gray-600 dark:text-gray-400">Explore blocks</p>
              </div>
            </div>
          </a>
          
          <a href="/accounts" className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 hover:border-blue-500 transition-colors group">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faUsers} className="h-8 w-8 text-yellow-400 group-hover:text-yellow-300" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white group-hover:text-yellow-300">Accounts</h3>
                <p className="text-gray-600 dark:text-gray-400">Top accounts</p>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
}
