'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faSearch, 
  faUsers, 
  faExchangeAlt, 
  faCube, 
  faShieldAlt, 
  faArrowUp,
  faArrowsAltH,
  faCode,
  faExternalLinkAlt,
  faFileContract,
  faGasPump,
  faTwitter,
  faTelegram,
  faLink,
  faWallet,
  faCoins,
  faPercentage,
  faChartLine,
  faSort,
  faSortUp,
  faSortDown,
  faCopy,
  faEye
} from '@fortawesome/free-solid-svg-icons';
import { faTwitter as fabTwitter, faTelegram as fabTelegram } from '@fortawesome/free-brands-svg-icons';
import useTakeSnapshot from '@/hooks/useSnapshot';
import { rpcUrls, tokenAddress } from '@/context/AppKit';
import { useAppKitNetworkCore } from '@reown/appkit/react';
import { formatEther } from 'ethers';
import BigNumber from 'bignumber.js';

export default function TopHoldersPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const {chainId} = useAppKitNetworkCore();
  const [topHolders, setTopHolders] = useState([]);
  const [sortBy, setSortBy] = useState('balance');
  const [sortOrder, setSortOrder] = useState('desc');

  const {snapshot, isLoading, error} = useTakeSnapshot({
    rpcUrl: rpcUrls[chainId],
    tokenAddress: tokenAddress[chainId],
    fromBlock: 0,
    toBlock: "latest"
  });

  // Verarbeite Snapshot-Daten für Top Holders
  useEffect(() => {
    const createTopHolders = () => {
      if(!snapshot) return;
      if (snapshot.length > 0) {
        // Sortiere nach Balance (absteigend) und nehme die Top 100
        const sortedHolders = snapshot
          .sort((a, b) => {
            const balanceA = parseFloat(new BigNumber(a.balance).shiftedBy(-18).toString());
            const balanceB = parseFloat(new BigNumber(b.balance).shiftedBy(-18).toString());
            return balanceB - balanceA;
          })
          .slice(0, 100)
          .map((holder, index) => {
            const balance = parseFloat(new BigNumber(holder.balance).shiftedBy(-18).toString());
            const balanceValue = (balance * 2).toFixed(2); // Annahme: 1 Token = $2
            
            return {
              rank: index + 1,
              address: holder.address,
              name: getHolderName(holder.address),
              balance: balance.toLocaleString('en-US', { maximumFractionDigits: 2 }),
              balanceValue: `$${parseFloat(balanceValue).toLocaleString('en-US', { maximumFractionDigits: 2 })}`,
              percentage: calculatePercentage(balance, snapshot),
              txCount: holder.count,
              type: getAccountType(holder.address)
            };
          });

        setTopHolders(sortedHolders);
      }
    }

    createTopHolders();
  }, [snapshot]);

  const getHolderName = (address) => {
    // Bekannte Adressen mit Namen
    const knownAddresses = {
      '******************************************': 'Binance Hot Wallet',
      '******************************************': 'Binance 14',
      '******************************************': 'Kraken Wallet',
      '******************************************': 'Uniswap V3',
      '******************************************': 'Binance 14',
      '******************************************': 'Gnosis Safe',
      '******************************************': 'Gemini Exchange',
      '******************************************': 'Bitfinex Wallet',
      '******************************************': 'ETHAI Presale'
    };
    
    return knownAddresses[address] || 'Unknown';
  };

  const getAccountType = (address) => {
    // Bestimme Account-Typ basierend auf Adresse oder anderen Kriterien
    const exchangeAddresses = [
      '******************************************',
      '******************************************',
      '******************************************',
      '******************************************',
      '******************************************',
      '******************************************'
    ];
    
    const contractAddresses = [
      '******************************************',
      '******************************************'
    ];
    
    if (exchangeAddresses.includes(address)) return 'Exchange';
    if (contractAddresses.includes(address)) return 'Contract';
    return 'Wallet';
  };

  const calculatePercentage = (balance, allHolders) => {
    const totalSupply = ********; // Feste Gesamtsupply
    const percentage = (balance / totalSupply) * 100;
    return `${percentage.toFixed(2)}%`;
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const getSortIcon = (field) => {
    if (sortBy !== field) return faSort;
    return sortOrder === 'asc' ? faSortUp : faSortDown;
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const formatAddress = (address) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const getAccountTypeColor = (type) => {
    switch (type) {
      case 'Exchange':
        return 'text-blue-600 dark:text-blue-400';
      case 'Contract':
        return 'text-green-600 dark:text-green-400';
      case 'Wallet':
        return 'text-purple-600 dark:text-purple-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  // Berechne Statistiken
  const totalHolders = snapshot ? snapshot.length : 0;
  const totalSupply = ********;
  const totalValue = (totalSupply * 2).toFixed(2); // Annahme: 1 Token = $2
  const totalTransactions = snapshot ? snapshot.length * 50 : 0; // Mock: durchschnittlich 50 Transaktionen pro Holder

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
              Top Token Holders
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
              Explore the largest token holders on Etherchain. Track balances, transaction counts, and holder types.
            </p>
          </div>
        </div>
      </div>

      {/* Search Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 p-6 shadow-sm">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Search Holders</h2>
              <p className="text-gray-600 dark:text-gray-400">Find holders by address or name</p>
            </div>
            <div className="w-full md:w-96">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FontAwesomeIcon icon={faSearch} className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by address or name..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md leading-5 bg-white dark:bg-gray-900 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:focus:placeholder-gray-500 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faUsers} className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Holders</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalHolders ? totalHolders.toLocaleString() : '0'}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faCoins} className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Supply</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalSupply ? totalSupply.toLocaleString('en-US', { maximumFractionDigits: 2 }) : '0'}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faExchangeAlt} className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Transactions</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalTransactions ? totalTransactions.toLocaleString() : '0'}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faChartLine} className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Market Value</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">${totalValue ? parseFloat(totalValue).toLocaleString('en-US', { maximumFractionDigits: 2 }) : '0'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Holders Table */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Top Token Holders by Balance</h2>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading holders...</span>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center py-12">
              <span className="text-red-600 dark:text-red-400">Error loading data: {error.message}</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Rank
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Address
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Balance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Percentage
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Txn Count
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Type
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                  {topHolders
                    .filter(holder => 
                      searchQuery === '' || 
                      holder.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      holder.name.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((holder) => (
                    <tr key={holder.rank} className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        #{holder.rank}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FontAwesomeIcon icon={faWallet} className="h-4 w-4 text-blue-600 dark:text-blue-400 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {formatAddress(holder.address)}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {holder.address}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {holder.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {holder.balance} ETHAI
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {holder.balanceValue}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {holder.percentage}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {holder.txCount.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getAccountTypeColor(holder.type)}`}>
                          {holder.type}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
