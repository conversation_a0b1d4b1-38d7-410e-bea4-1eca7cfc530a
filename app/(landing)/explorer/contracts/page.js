'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faSearch, 
  faFileContract, 
  faExchangeAlt, 
  faCube, 
  faUsers, 
  faShieldAlt, 
  faArrowUp,
  faCoins,
  faArrowsAltH,
  faCode,
  faExternalLinkAlt,
  faGasPump,
  faTwitter,
  faTelegram,
  faLink,
  faCheckCircle,
  faTimesCircle,
  faSpinner,
  faCopy,
  faEye,
  faSort,
  faSortUp,
  faSortDown,
  faShield,
  faCodeBranch,
  faCalendarAlt,
  faUser
} from '@fortawesome/free-solid-svg-icons';
import { faTwitter as fabTwitter, faTelegram as fabTelegram } from '@fortawesome/free-brands-svg-icons';

export default function VerifiedContractsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [verifiedContracts, setVerifiedContracts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');

  // Mock data for verified contracts
  useEffect(() => {
    const mockContracts = [
      {
        id: 1,
        name: 'Etherchain Token',
        address: '******************************************',
        compiler: 'Solidity 0.8.19',
        optimization: 'Yes',
        runs: 200,
        verifiedAt: '2024-01-15T10:30:00Z',
        txCount: 1250,
        balance: '1,250.50 ETHAI',
        creator: '******************************************',
        license: 'MIT',
        verified: true
      },
      {
        id: 2,
        name: 'Staking Contract',
        address: '******************************************',
        compiler: 'Solidity 0.8.20',
        optimization: 'Yes',
        runs: 500,
        verifiedAt: '2024-01-10T14:20:00Z',
        txCount: 890,
        balance: '5,750.25 ETHAI',
        creator: '******************************************',
        license: 'GPL-3.0',
        verified: true
      },
      {
        id: 3,
        name: 'DAO Governance',
        address: '******************************************',
        compiler: 'Solidity 0.8.18',
        optimization: 'No',
        runs: 0,
        verifiedAt: '2024-01-05T09:15:00Z',
        txCount: 456,
        balance: '2,100.75 ETHAI',
        creator: '******************************************',
        license: 'MIT',
        verified: true
      },
      {
        id: 4,
        name: 'NFT Marketplace',
        address: '******************************************',
        compiler: 'Solidity 0.8.21',
        optimization: 'Yes',
        runs: 1000,
        verifiedAt: '2024-01-20T16:45:00Z',
        txCount: 2340,
        balance: '8,900.00 ETHAI',
        creator: '******************************************',
        license: 'Apache-2.0',
        verified: true
      },
      {
        id: 5,
        name: 'DeFi Protocol',
        address: '******************************************',
        compiler: 'Solidity 0.8.19',
        optimization: 'Yes',
        runs: 300,
        verifiedAt: '2024-01-12T11:30:00Z',
        txCount: 1678,
        balance: '12,500.50 ETHAI',
        creator: '******************************************',
        license: 'MIT',
        verified: true
      }
    ];

    setTimeout(() => {
      setVerifiedContracts(mockContracts);
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };

  const formatAddress = (address) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredContracts = verifiedContracts.filter(contract =>
    contract.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contract.address.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedContracts = [...filteredContracts].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];

    if (sortBy === 'verifiedAt') {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });

  const getSortIcon = (field) => {
    if (sortBy !== field) return faSort;
    return sortOrder === 'asc' ? faSortUp : faSortDown;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-6 lg:mb-0">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Verified Contracts
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Browse and interact with verified smart contracts on Etherchain
              </p>
            </div>
            
            {/* Search Bar */}
            <div className="relative w-full lg:w-96">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={handleSearch}
                placeholder="Search contracts by name or address..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md leading-5 bg-white dark:bg-gray-900 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 dark:focus:placeholder-gray-500 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faFileContract} className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Contracts</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">1,234</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faCheckCircle} className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Verified</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">987</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faExchangeAlt} className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Transactions</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">45.2M</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-800 shadow-sm">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FontAwesomeIcon icon={faCoins} className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Balance</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">$2.1B</p>
              </div>
            </div>
          </div>
        </div>

        {/* Contracts Table */}
        <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Verified Smart Contracts</h2>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading contracts...</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-800">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Contract
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Address
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Compiler
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Balance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Tx Count
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Verified
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                  {sortedContracts.map((contract) => (
                    <tr key={contract.id} className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FontAwesomeIcon icon={faFileContract} className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {contract.name}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {contract.license}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-300 font-mono">
                          {formatAddress(contract.address)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-300">
                          {contract.compiler}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {contract.optimization ? `Optimized (${contract.runs} runs)` : 'Not optimized'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                        {contract.balance}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
                        {contract.txCount.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FontAwesomeIcon 
                            icon={contract.verified ? faCheckCircle : faTimesCircle} 
                            className={`h-4 w-4 mr-2 ${contract.verified ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`} 
                          />
                          <span className={`text-sm font-medium ${contract.verified ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                            {contract.verified ? 'Verified' : 'Not Verified'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => copyToClipboard(contract.address)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                            title="Copy address"
                          >
                            <FontAwesomeIcon icon={faCopy} className="h-4 w-4" />
                          </button>
                          <a
                            href={`/contract/${contract.address}`}
                            className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                            title="View contract"
                          >
                            <FontAwesomeIcon icon={faEye} className="h-4 w-4" />
                          </a>
                          <a
                            href={`/contract/${contract.address}/code`}
                            className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300"
                            title="View source code"
                          >
                            <FontAwesomeIcon icon={faCode} className="h-4 w-4" />
                          </a>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
