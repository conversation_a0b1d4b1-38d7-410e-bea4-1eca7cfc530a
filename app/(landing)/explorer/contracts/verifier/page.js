'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faSearch, 
  faFileContract, 
  faExchangeAlt, 
  faCube, 
  faUsers, 
  faShieldAlt, 
  faArrowUp,
  faCoins,
  faArrowsAltH,
  faCode,
  faExternalLinkAlt,
  faGasPump,
  faLink,
  faCheckCircle,
  faTimesCircle,
  faSpinner,
  faCopy,
  faEye,
  faShield,
  faCodeBranch,
  faCalendarAlt,
  faUser,
  faUpload,
  faFileCode,
  faCog,
  faInfoCircle,
  faWarning,
  faCheck,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { faTwitter, faTelegram } from '@fortawesome/free-brands-svg-icons';
import GradientButton from '@/app/components/GradientButton';

export default function ContractVerificationPage() {
  const [contractAddress, setContractAddress] = useState('');
  const [compilerVersion, setCompilerVersion] = useState('');
  const [optimization, setOptimization] = useState(false);
  const [runs, setRuns] = useState(200);
  const [license, setLicense] = useState('MIT');
  const [sourceCode, setSourceCode] = useState('');
  const [constructorArguments, setConstructorArguments] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [errors, setErrors] = useState([]);

  const compilerVersions = [
    '0.8.24+commit.e11b9ed9',
    '0.8.23+commit.f704f362',
    '0.8.22+commit.4fc1097e',
    '0.8.21+commit.d9974bed',
    '0.8.20+commit.a1b79de6',
    '0.8.19+commit.7dd6d404',
    '0.8.18+commit.87f05d96',
    '0.8.17+commit.8df45f5f',
    '0.8.16+commit.07a7930e',
    '0.8.15+commit.e14a2714'
  ];

  const licenses = [
    'MIT',
    'GPL-3.0',
    'GPL-2.0',
    'LGPL-3.0',
    'LGPL-2.1',
    'BSD-3-Clause',
    'BSD-2-Clause',
    'Apache-2.0',
    'Unlicense',
    'Custom'
  ];

  const handleVerification = async (e) => {
    e.preventDefault();
    setIsVerifying(true);
    setErrors([]);
    
    // Simulate verification process
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70% success rate
      if (success) {
        setVerificationStatus('success');
      } else {
        setVerificationStatus('error');
        setErrors(['Contract verification failed. Please check your source code and parameters.']);
      }
      setIsVerifying(false);
    }, 3000);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <FontAwesomeIcon icon={faFileContract} className="text-blue-400 text-2xl" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Verify & publish contract</h1>
                <p className="text-gray-600 dark:text-gray-400">Verify your smart contract source code</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <FontAwesomeIcon icon={faTwitter} className="text-gray-850 dark:text-gray-200 hover:text-blue-400 cursor-pointer" />
              <FontAwesomeIcon icon={faTelegram} className="text-gray-850 dark:text-gray-200 hover:text-blue-400 cursor-pointer" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg shadow-xl p-6">
          <form onSubmit={handleVerification} className="space-y-6">
            {/* Contract Address */}
            <div>
              <label className="block text-sm font-medium text-gray-850 dark:text-gray-200 mb-2">
                Contract Address *
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={contractAddress}
                  onChange={(e) => setContractAddress(e.target.value)}
                  placeholder="0x..."
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-600 rounded-lg text-gray-850 dark:text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
                <FontAwesomeIcon icon={faFileContract} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
            </div>

            {/* Compiler Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-850 dark:text-gray-200 mb-2">
                  Compiler Version *
                </label>
                <select
                  value={compilerVersion}
                  onChange={(e) => setCompilerVersion(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-600 rounded-lg text-gray-850 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Select version</option>
                  {compilerVersions.map((version) => (
                    <option key={version} value={version}>{version}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-850 dark:text-gray-200 mb-2">
                  Optimization
                </label>
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={optimization}
                    onChange={(e) => setOptimization(e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-50 dark:bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <span className="text-gray-850 dark:text-gray-200">Enabled</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-850 dark:text-gray-200 mb-2">
                  Runs
                </label>
                <input
                  type="number"
                  value={runs}
                  onChange={(e) => setRuns(parseInt(e.target.value))}
                  className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-600 rounded-lg text-gray-850 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="0"
                />
              </div>
            </div>

            {/* License */}
            <div>
              <label className="block text-sm font-medium text-gray-850 dark:text-gray-200 mb-2">
                Open Source License Type *
              </label>
              <select
                value={license}
                onChange={(e) => setLicense(e.target.value)}
                className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-600 rounded-lg text-gray-850 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                {licenses.map((lic) => (
                  <option key={lic} value={lic}>{lic}</option>
                ))}
              </select>
            </div>

            {/* Source Code */}
            <div>
              <label className="block text-sm font-medium text-gray-850 dark:text-gray-200 mb-2">
                Contract Source Code *
              </label>
              <textarea
                value={sourceCode}
                onChange={(e) => setSourceCode(e.target.value)}
                placeholder="// SPDX-License-Identifier: MIT&#10;pragma solidity ^0.8.0;&#10;&#10;contract MyContract {&#10;    // Your contract code here&#10;}"
                rows={15}
                className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-600 rounded-lg text-gray-850 dark:text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                required
              />
            </div>

            {/* Constructor Arguments */}
            <div>
              <label className="block text-sm font-medium text-gray-850 dark:text-gray-200 mb-2">
                Constructor Arguments (ABI-encoded)
              </label>
              <input
                type="text"
                value={constructorArguments}
                onChange={(e) => setConstructorArguments(e.target.value)}
                placeholder="0x..."
                className="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-600 rounded-lg text-gray-850 dark:text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
              />
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                If your contract has constructor arguments, provide them in ABI-encoded format
              </p>
            </div>

            {/* Verification Status */}
            {verificationStatus && (
              <div className={`p-4 rounded-lg ${
                verificationStatus === 'success' 
                  ? 'bg-green-900 border border-green-700' 
                  : 'bg-red-900 border border-red-700'
              }`}>
                <div className="flex items-center space-x-2">
                  <FontAwesomeIcon 
                    icon={verificationStatus === 'success' ? faCheckCircle : faTimesCircle} 
                    className={`text-lg ${
                      verificationStatus === 'success' ? 'text-green-400' : 'text-red-400'
                    }`} 
                  />
                  <span className="font-medium">
                    {verificationStatus === 'success' 
                      ? 'Contract verified successfully!' 
                      : 'Verification failed'
                    }
                  </span>
                </div>
                {verificationStatus === 'success' && (
                  <p className="text-green-600 dark:text-green-400 mt-2">
                    Your contract has been verified and is now publicly accessible on the blockchain explorer.
                  </p>
                )}
                {errors.length > 0 && (
                  <ul className="text-red-600 dark:text-red-400 mt-2 list-disc list-inside">
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                )}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end">
              <GradientButton
                variant={2}
                type="submit"
                disabled={isVerifying}
                className={`px-8 py-3 rounded-lg font-medium flex items-center space-x-2 ${
                  isVerifying
                    ? 'bg-gray-600 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800'
                }`}
              >
                {isVerifying ? (
                  <>
                    <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                    <span>Verifying...</span>
                  </>
                ) : (
                  <>
                    <FontAwesomeIcon icon={faShield} />
                    <span>Verify Contract</span>
                  </>
                )}
              </GradientButton>
            </div>
          </form>
        </div>

        {/* Help Section */}
        <div className="mt-8 bg-gray-50 dark:bg-gray-900 rounded-lg shadow-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
            <FontAwesomeIcon icon={faInfoCircle} className="text-blue-400" />
            <span>Verification Help</span>
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-850 dark:text-gray-200 mb-2">What is contract verification?</h4>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Contract verification allows users to view and interact with your smart contract's source code, 
                making it transparent and trustworthy for the community.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-850 dark:text-gray-200 mb-2">Requirements</h4>
              <ul className="text-gray-600 dark:text-gray-400 text-sm space-y-1">
                <li>• Contract must be deployed on the network</li>
                <li>• Source code must match the deployed bytecode</li>
                <li>• Compiler settings must be identical</li>
                <li>• Constructor arguments must be provided if applicable</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
