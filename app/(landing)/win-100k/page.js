'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faGift,
  faTrophy,
  faUsers,
  faRocket,
  faCheckCircle,
  faStar,
  faCoins,
  faShieldAlt,
  faArrowRight,
  faPlay
} from '@fortawesome/free-solid-svg-icons';
import GradientButton from '@/app/components/GradientButton';

export default function Win100KPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [participants, setParticipants] = useState(() => {
    // Lade Teilnehmerzahl aus localStorage oder verwende Standardwert
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('etherchain_participants');
      return saved ? parseInt(saved, 10) : 15420;
    }
    return 15420;
  });
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    // Simuliere Ladezeit für iframe
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    // Countdown Timer
    const countdown = setInterval(() => {
      const now = new Date().getTime();
      const endDate = new Date('2025-12-01T00:00:00').getTime();
      const distance = endDate - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      } else {
        // Timer ist abgelaufen
        setTimeLeft({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0
        });
      }
    }, 1000);

    // Simuliere steigende Teilnehmerzahl
    const participantTimer = setInterval(() => {
      setParticipants(prev => {
        const newValue = prev + Math.floor(Math.random() * 3) + 1;
        // Speichere neue Teilnehmerzahl im localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('etherchain_participants', newValue.toString());
        }
        return newValue;
      });
    }, 5000);

    return () => {
      clearTimeout(timer);
      clearInterval(countdown);
      clearInterval(participantTimer);
    };
  }, []);

  const features = [
    {
      icon: faTrophy,
      title: "$100,000 Prize",
      description: "The main prize is $100,000 in Etherchain tokens"
    },
    {
      icon: faUsers,
      title: "Community",
      description: "Join the Etherchain community and benefit from the revolution"
    },
    {
      icon: faRocket,
      title: "Early Access",
      description: "Exclusive access to new features and beta tests"
    },
    {
      icon: faShieldAlt,
      title: "Secure & Fair",
      description: "Transparent and fair drawing with blockchain verification"
    }
  ];

  const prizes = [
    { place: "1.", amount: "$100,000", description: "Grand Prize" },
    { place: "2.", amount: "$25,000", description: "Second Place" },
    { place: "3.", amount: "$10,000", description: "Third Place" },
    { place: "4-10", amount: "$1,000", description: "Top 10 Winners" },
    { place: "11-100", amount: "$100", description: "Top 100 Winners" }
  ];

  return (
    <div className="min-h-screen bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-white ">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100/50 via-purple-100/50 to-indigo-100/50 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-indigo-900/20"></div>
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        
        <div className="relative z-10 container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <div className="flex justify-center mb-6">
              <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-1 rounded-full">
                <div className="bg-white dark:bg-gray-900 px-6 py-2 rounded-full">
                  <span className="text-yellow-600 dark:text-yellow-400 font-bold text-sm">🎉 GIVEAWAY ACTIVE 🎉</span>
                </div>
              </div>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 dark:from-yellow-400 dark:via-orange-500 dark:to-red-500 bg-clip-text text-transparent">
              Win $100,000
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-900 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              Join the biggest Etherchain giveaway of all time! 
              Be part of the decentralized AI and blockchain technology revolution.
            </p>

            {/* Countdown Timer */}
            <div className="grid grid-cols-4 gap-4 max-w-md mx-auto mb-8">
              {Object.entries(timeLeft).map(([unit, value]) => (
                <div key={unit} className="bg-white/80 dark:bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-lg">
                  <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{value.toString().padStart(2, '0')}</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 uppercase">{unit}</div>
                </div>
              ))}
            </div>

            {/* Stats */}
            <div className="flex justify-center items-center gap-8 mb-12">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">{participants.toLocaleString()}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Participants</div>
              </div>
              <div className="w-px h-12 bg-gray-300 dark:bg-gray-600"></div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400">$100,000</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Grand Prize</div>
              </div>
              <div className="w-px h-12 bg-gray-300 dark:bg-gray-600"></div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">100</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Winners</div>
              </div>
            </div>

            <button className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-gray-900 font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
              <FontAwesomeIcon icon={faGift} className="mr-2" />
              Enter Now
            </button>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-gray-100/50 dark:bg-gray-900/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">Why Etherchain?</h2>
            <p className="text-xl text-gray-900 dark:text-gray-400 max-w-3xl mx-auto">
              Discover the future of decentralized AI and become part of a revolutionary technology
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white/80 dark:bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:border-blue-500 transition-all duration-300 group shadow-lg">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <FontAwesomeIcon icon={feature.icon} className="text-2xl text-white" />
                </div>
                <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">{feature.title}</h3>
                <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Prizes Section */}
      <div className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">Prizes</h2>
            <p className="text-xl text-gray-900 dark:text-gray-300">Over $150,000 in prizes to win</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {prizes.map((prize, index) => (
              <div key={index} className={`bg-gradient-to-br rounded-xl p-6 text-center border shadow-lg ${
                index === 0 
                  ? 'from-yellow-100/80 to-orange-100/80 border-yellow-300 dark:from-yellow-400/20 dark:to-orange-500/20 dark:border-yellow-500/50' 
                  : 'from-white/80 to-gray-50/80 border-gray-200 dark:from-gray-800/50 dark:to-gray-700/50 dark:border-gray-600'
              }`}>
                <div className={`text-4xl font-bold mb-2 ${
                  index === 0 ? 'text-yellow-600 dark:text-yellow-400' : 'text-blue-600 dark:text-blue-400'
                }`}>
                  {prize.place}
                </div>
                <div className={`text-2xl font-bold mb-2 ${
                  index === 0 ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-900 dark:text-white'
                }`}>
                  {prize.amount}
                </div>
                <div className="text-gray-600 dark:text-gray-400">{prize.description}</div>
                {index === 0 && (
                  <div className="mt-4">
                    <FontAwesomeIcon icon={faStar} className="text-yellow-500 dark:text-yellow-400 text-xl" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Giveaway Embed Section */}
      <div className="py-20 bg-gray-100/50 dark:bg-gray-900/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">How to Enter</h2>
            <p className="text-xl text-gray-900 dark:text-gray-300 mb-8">
              Fill out the form and increase your chances of winning through various actions
            </p>
            
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <div className="flex items-center gap-2 bg-white/80 dark:bg-gray-900/50 px-4 py-2 rounded-full border border-gray-200 dark:border-gray-600 shadow-sm">
                <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 dark:text-green-400" />
                <span className="text-sm text-gray-900 dark:text-gray-300">Connect Wallet</span>
              </div>
              <div className="flex items-center gap-2 bg-white/80 dark:bg-gray-900/50 px-4 py-2 rounded-full border border-gray-200 dark:border-gray-600 shadow-sm">
                <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 dark:text-green-400" />
                <span className="text-sm text-gray-900 dark:text-gray-300">Follow Social Media</span>
              </div>
              <div className="flex items-center gap-2 bg-white/80 dark:bg-gray-900/50 px-4 py-2 rounded-full border border-gray-200 dark:border-gray-600 shadow-sm">
                <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 dark:text-green-400" />
                <span className="text-sm text-gray-900 dark:text-gray-300">Join Community</span>
              </div>
            </div>
          </div>

          <div className="max-w-4xl mx-auto">
            {isLoading ? (
              <div className="bg-white/80 dark:bg-gray-900/50 rounded-xl p-12 text-center border border-gray-200 dark:border-gray-700 shadow-lg">
                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading giveaway...</p>
              </div>
            ) : (
              <div className="bg-white/80 dark:bg-gray-900/50 rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 shadow-2xl">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
                  <h3 className="text-xl font-bold text-center text-white">
                    <FontAwesomeIcon icon={faGift} className="mr-2" />
                    Etherchain 100K Giveaway
                  </h3>
                </div>
                <div className="h-[600px]">
                  <iframe 
                    src="https://gleam.io/9f42w/etherchain-100000-giveaway" 
                    frameBorder="0" 
                    allowFullScreen 
                    className="w-full h-full"
                    title="Etherchain Giveaway"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 bg-gradient-to-r from-blue-100/50 to-purple-100/50 dark:from-blue-900/20 dark:to-purple-900/20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6 text-gray-900 dark:text-white">Ready for the Future?</h2>
          <p className="text-xl text-gray-900 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Become part of the Etherchain revolution and win your share of $100,000! 
            The future of decentralized AI awaits you.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <GradientButton variant={1} className="text-gray-900 font-bold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
              <FontAwesomeIcon icon={faGift} className="mr-2" />
              Enter Now
            </GradientButton>
            <GradientButton variant={2} className="bg-white/80 dark:bg-gray-900/50 hover:bg-gray-100/80 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white font-bold py-4 px-8 rounded-full border border-gray-200 dark:border-gray-600 transition-all duration-300 shadow-lg">
              <FontAwesomeIcon icon={faPlay} className="mr-2" />
              Learn More
            </GradientButton>
          </div>
        </div>
      </div>

      {/* Footer Info */}
      <div className="py-12 bg-gray-100 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800">
        <div className="container mx-auto px-4 text-center">
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div>
              <h4 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">Terms & Conditions</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Must be 18+ to enter. Winners will be notified via email.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">Fair & Transparent</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                All winners are selected through blockchain verification.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">Support</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Questions? Contact us via Telegram or Discord.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
