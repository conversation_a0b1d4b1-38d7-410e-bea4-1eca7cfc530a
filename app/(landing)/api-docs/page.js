'use client';

import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faCode,
  faBook,
  faRocket,
  faClock,
  faLightbulb,
  faTerminal
} from '@fortawesome/free-solid-svg-icons';

export default function ApiDocsPage() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <FontAwesomeIcon icon={faCode} className="mr-3 text-blue-600 dark:text-blue-400" />
                API Documentation
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Complete API documentation and developer tools for EtherchainAI
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Coming Soon Banner */}
        <div className="text-center mb-12">
          <div className="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 border border-blue-300 dark:border-blue-500/30 rounded-2xl p-12 mb-8 shadow-sm">
            <div className="mb-6">
              <FontAwesomeIcon 
                icon={faRocket} 
                className="text-6xl text-blue-600 dark:text-blue-400 mb-4 animate-pulse" 
              />
            </div>
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Coming Soon
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Our comprehensive API documentation is currently under development. 
              We're working hard to bring you the best developer experience.
            </p>
            <div className="flex items-center justify-center text-gray-500 dark:text-gray-400 mb-6">
              <FontAwesomeIcon icon={faClock} className="mr-2" />
              <span>Expected Launch: Q4 2025</span>
            </div>
          </div>
        </div>

        {/* Features Preview */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center mb-4">
              <FontAwesomeIcon icon={faBook} className="text-blue-600 dark:text-blue-400 text-xl mr-3" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">REST API</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Complete REST API documentation with examples and interactive testing
            </p>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center mb-4">
              <FontAwesomeIcon icon={faCode} className="text-purple-600 dark:text-purple-400 text-xl mr-3" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">GraphQL</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Interactive GraphQL playground for advanced data querying
            </p>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center mb-4">
              <FontAwesomeIcon icon={faLightbulb} className="text-yellow-600 dark:text-yellow-400 text-xl mr-3" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">SDKs & Libraries</h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              Client libraries and SDKs for popular programming languages
            </p>
          </div>
        </div>

        {/* Search Section */}
        <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 mb-8 shadow-sm">
          <div className="flex items-center mb-4">
            <FontAwesomeIcon icon={faSearch} className="text-gray-500 dark:text-gray-400 mr-3" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Search API Documentation</h3>
          </div>
          <div className="relative">
            <input
              type="text"
              placeholder="Search for endpoints, methods, or topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
            />
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-white">
              <FontAwesomeIcon icon={faSearch} />
            </button>
          </div>
        </div>

        {/* Quick Links */}
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Getting Started</h3>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li>• API Authentication</li>
              <li>• Rate Limits</li>
              <li>• Error Handling</li>
              <li>• Best Practices</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">API Endpoints</h3>
            <ul className="space-y-2 text-gray-600 dark:text-gray-400">
              <li>• Blockchain Data</li>
              <li>• Transaction History</li>
              <li>• Token Information</li>
              <li>• Smart Contract Data</li>
            </ul>
          </div>
        </div>

        {/* Sample API Preview */}
        <div className="mt-12">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Sample API Endpoints</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FontAwesomeIcon icon={faTerminal} className="text-green-600 dark:text-green-400 mr-3" />
                <span className="text-gray-600 dark:text-gray-400 font-mono">GET /api/v1/transactions</span>
              </div>
              <pre className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-sm text-gray-850 dark:text-gray-200 overflow-x-auto">
{`{
  "data": [
    {
      "hash": "0x...",
      "from": "0x...",
      "to": "0x...",
      "value": "1000000000000000000",
      "timestamp": 1640995200
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1000
  }
}`}
              </pre>
            </div>

            <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FontAwesomeIcon icon={faTerminal} className="text-blue-600 dark:text-blue-400 mr-3" />
                <span className="text-gray-600 dark:text-gray-400 font-mono">GET /api/v1/blocks/{'{blockNumber}'}</span>
              </div>
              <pre className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-sm text-gray-850 dark:text-gray-200 overflow-x-auto">
{`{
  "data": {
    "number": 12345,
    "hash": "0x...",
    "timestamp": 1640995200,
    "transactions": 150,
    "gasUsed": "15000000",
    "gasLimit": "30000000"
  }
}`}
              </pre>
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mt-4 text-sm">
            These are just previews. The complete API documentation will be available soon.
          </p>
        </div>
      </div>
    </div>
  );
}
