'use client';

import { useState, useEffect } from 'react';
import HeroSection from '../components/HeroSection';
import Image from 'next/image';
import TokenomicsSection from '../components/TokenomicsSection';
import CTASection from '../components/CTASection';
import FAQSection from '../components/FAQSection';
import BrandsSection from '../components/BrandsSection';
import PoISection from '../components/PoISection';
import AIVMSection from '../components/AIVMSection';
import FeaturesSection from '../components/FeaturesSection';
import RoadmapSection from '../components/RoadmapSection';
import Divider from '../components/Divider';
import BrandSlider from '../components/BrandSlider';
import BlogSection from '../components/BlogSection';
import CommunitySection from '../components/CommunitySection';
import MetricsSection from '../components/MetricsSection';
import { ToastContainer } from 'react-toastify';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950">

      {/* Social Media Sidebar - Fixed position like lightchain.ai */}
      <div className="fixed left-4 top-1/2 transform -translate-y-1/2 z-20 hidden lg:flex flex-col space-y-4">
        <a href="https://x.com/EtherchainAI" className="w-12 h-12 bg-white dark:bg-gray-900 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 border border-gray-200 dark:border-gray-700">
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 24 24">
            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
          </svg>
        </a>
        <a href="https://t.me/EtherchainProtocol" className="w-12 h-12 bg-white dark:bg-gray-900 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 border border-gray-200 dark:border-gray-700">
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 24 24">
            <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
          </svg>
        </a>
        <a href="https://linktr.ee/etherchainai" className="w-12 h-12 bg-white dark:bg-gray-900 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 border border-gray-200 dark:border-gray-700">
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.953 15.066c-.08.163-.08.324-.08.486.08.517.528.897 1.052.897.524 0 .973-.38 1.052-.897 0-.162 0-.323-.08-.486-.592-1.136-1.888-1.136-2.944 0zm4.847-9.518c-.08-.162-.08-.323-.08-.485-.08-.517-.528-.897-1.052-.897-.524 0-.973.38-1.052.897 0 .162 0 .323.08.485.592 1.137 1.888 1.137 2.944 0zm-2.944 3.772c-.08.163-.08.324-.08.486.08.517.528.897 1.052.897.524 0 .973-.38 1.052-.897 0-.162 0-.323-.08-.486-.592-1.136-1.888-1.136-2.944 0zm0 3.772c-.08.163-.08.324-.08.486.08.517.528.897 1.052.897.524 0 .973-.38 1.052-.897 0-.162 0-.323-.08-.486-.592-1.136-1.888-1.136-2.944 0z"/>
          </svg>
        </a>
        <a href="https://news.etherchain.ai" className="w-12 h-12 bg-white dark:bg-gray-900 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 border border-gray-200 dark:border-gray-700">
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 24 24">
            <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
          </svg>
        </a>
      </div>
      
      {/* Hero Section */}
      <HeroSection />
      
      <Divider />

      {/* Metrics Section */}
      <MetricsSection />

      {/* Brands/Partners Section */}
      <BrandSlider />

      {/* Wave Separator - Bottom */}
      <Divider />

      {/* PoI Section with Background SVGs */}
      <PoISection />

      {/* Wave Separator */}
      <Divider />

      {/* AIVM Section */}
      <AIVMSection />

      {/* Wave Separator */}
      <Divider />
      {/* Features Section */}
      <FeaturesSection />

      <Divider />

      {/* Roadmap Section */}
      <RoadmapSection />

      <Divider />
      {/* Tokenomics Section */}
      <TokenomicsSection />

      <Divider />
      
      <CommunitySection />

      <Divider />

      <BlogSection />

      <Divider />

      {/* FAQ Section */}
      <FAQSection />

      <Divider />

      {/* CTA Section */}
      <CTASection />
      
      <ToastContainer position='top-right' />
    </div>
  );
}
