'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function ForumPage() {
    const [activeTab, setActiveTab] = useState('all');
    const [searchQuery, setSearchQuery] = useState('');
    const [showMobileTabs, setShowMobileTabs] = useState(false);

    const categories = [
        {
            id: 'general',
            name: 'General Discussion',
            description: 'General topics about Etherchain and blockchain technology',
            icon: '💬',
            color: 'bg-blue-500',
            topics: 24,
            posts: 156
        },
        {
            id: 'technical',
            name: 'Technical Support',
            description: 'Technical questions and support for developers',
            icon: '⚙️',
            color: 'bg-green-500',
            topics: 18,
            posts: 89
        },
        {
            id: 'development',
            name: 'Development',
            description: 'Discussion about building on Etherchain',
            icon: '🚀',
            color: 'bg-purple-500',
            topics: 32,
            posts: 203
        },
        {
            id: 'trading',
            name: 'Trading & Markets',
            description: 'Trading discussions and market analysis',
            icon: '📈',
            color: 'bg-yellow-500',
            topics: 15,
            posts: 67
        },
        {
            id: 'announcements',
            name: 'Announcements',
            description: 'Official announcements and updates',
            icon: '📢',
            color: 'bg-red-500',
            topics: 8,
            posts: 45
        },
        {
            id: 'community',
            name: 'Community',
            description: 'Community events and meetups',
            icon: '👥',
            color: 'bg-indigo-500',
            topics: 12,
            posts: 78
        }
    ];

    const recentTopics = [
        {
            id: 1,
            title: 'How to integrate Etherchain into my dApp?',
            author: 'CryptoDev',
            category: 'development',
            replies: 12,
            views: 234,
            lastActivity: '2 hours ago',
            isSticky: false,
            isLocked: false
        },
        {
            id: 2,
            title: 'Etherchain Mainnet Launch - What to Expect',
            author: 'EtherchainTeam',
            category: 'announcements',
            replies: 45,
            views: 1203,
            lastActivity: '1 day ago',
            isSticky: true,
            isLocked: false
        },
        {
            id: 3,
            title: 'Best practices for smart contract development',
            author: 'BlockchainGuru',
            category: 'technical',
            replies: 28,
            views: 567,
            lastActivity: '3 days ago',
            isSticky: false,
            isLocked: false
        },
        {
            id: 4,
            title: 'Token price discussion and predictions',
            author: 'TraderPro',
            category: 'trading',
            replies: 67,
            views: 892,
            lastActivity: '5 days ago',
            isSticky: false,
            isLocked: false
        },
        {
            id: 5,
            title: 'Community meetup in Berlin - Join us!',
            author: 'CommunityManager',
            category: 'community',
            replies: 15,
            views: 234,
            lastActivity: '1 week ago',
            isSticky: false,
            isLocked: false
        }
    ];

    const filteredTopics = recentTopics.filter(topic => {
        if (activeTab === 'all') return true;
        return topic.category === activeTab;
    });

    const allTabs = [
        { id: 'all', name: 'All Categories' },
        ...categories
    ];

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900 ">
            {/* Hero Section */}
            <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                    <div className="text-center">
                        <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                            Etherchain Community Forum
                        </h1>
                        <p className="text-xl text-purple-100 mb-8 max-w-3xl mx-auto">
                            Join the conversation with developers, traders, and enthusiasts. Share ideas, get support, and stay updated with the latest developments.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors duration-300">
                                Start New Discussion
                            </button>
                            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors duration-300">
                                Browse Categories
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Search and Filters */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 mb-8">
                    <div className="flex flex-col lg:flex-row gap-4 items-center">
                        <div className="flex-1 w-full">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search discussions..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    className="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                                />
                                <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                        
                        {/* Desktop Tab Navigation */}
                        <div className="hidden sm:flex gap-2">
                            <button
                                onClick={() => setActiveTab('all')}
                                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                    activeTab === 'all'
                                        ? 'bg-purple-600 text-white'
                                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                }`}
                            >
                                All
                            </button>
                            {categories.map((category) => (
                                <button
                                    key={category.id}
                                    onClick={() => setActiveTab(category.id)}
                                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                        activeTab === category.id
                                            ? 'bg-purple-600 text-white'
                                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                    }`}
                                >
                                    {category.name}
                                </button>
                            ))}
                        </div>

                        {/* Mobile Tab Toggle Button */}
                        <div className="sm:hidden w-full">
                            <button
                                onClick={() => setShowMobileTabs(!showMobileTabs)}
                                className="w-full bg-purple-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-between"
                            >
                                <span>{allTabs.find(tab => tab.id === activeTab)?.name}</span>
                                <svg
                                    className={`w-5 h-5 transition-transform ${showMobileTabs ? 'rotate-180' : ''}`}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Mobile Tab Overlay */}
                {showMobileTabs && (
                    <div className="sm:hidden fixed inset-0 z-50">
                        {/* Backdrop */}
                        <div 
                            className="absolute inset-0 bg-black/50"
                            onClick={() => setShowMobileTabs(false)}
                        ></div>
                        
                        {/* Tab Panel - slides up from bottom */}
                        <div className="absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-900 rounded-t-2xl transform transition-transform duration-300 ease-out" style={{ height: '33.333%' }}>
                            <div className="p-6">
                                <div className="flex items-center justify-between mb-6">
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                        Filter Categories
                                    </h3>
                                    <button
                                        onClick={() => setShowMobileTabs(false)}
                                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                    >
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                                
                                <div className="space-y-3 max-h-48 overflow-y-auto">
                                    {allTabs.map((tab) => (
                                        <button
                                            key={tab.id}
                                            onClick={() => {
                                                setActiveTab(tab.id);
                                                setShowMobileTabs(false);
                                            }}
                                            className={`w-full text-left px-4 py-3 rounded-lg font-medium transition-colors ${
                                                activeTab === tab.id
                                                    ? 'bg-purple-600 text-white'
                                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                                            }`}
                                        >
                                            {tab.name}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Categories Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                    {categories.map((category) => (
                        <div key={category.id} className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                            <div className="flex items-center mb-4">
                                <div className={`w-12 h-12 rounded-lg ${category.color} flex items-center justify-center text-2xl mr-4`}>
                                    {category.icon}
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                        {category.name}
                                    </h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {category.topics} topics, {category.posts} posts
                                    </p>
                                </div>
                            </div>
                            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                                {category.description}
                            </p>
                            <button className="w-full bg-purple-600 text-white py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors duration-300">
                                View Category
                            </button>
                        </div>
                    ))}
                </div>

                {/* Recent Discussions */}
                <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                            Recent Discussions
                        </h2>
                    </div>
                    <div className="divide-y divide-gray-200 dark:divide-gray-700">
                        {filteredTopics.map((topic) => (
                            <div key={topic.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0">
                                        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                                            {topic.author.charAt(0)}
                                        </div>
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center space-x-2 mb-2">
                                            {topic.isSticky && (
                                                <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium">
                                                    Sticky
                                                </span>
                                            )}
                                            {topic.isLocked && (
                                                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium">
                                                    Locked
                                                </span>
                                            )}
                                            <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                                                topic.category === 'announcements' ? 'bg-red-100 text-red-800' :
                                                topic.category === 'development' ? 'bg-purple-100 text-purple-800' :
                                                topic.category === 'technical' ? 'bg-green-100 text-green-800' :
                                                topic.category === 'trading' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-blue-100 text-blue-800'
                                            }`}>
                                                {categories.find(c => c.id === topic.category)?.name}
                                            </span>
                                        </div>
                                        <h3 className="text-lg font-medium text-gray-900 dark:text-white hover:text-purple-600 dark:hover:text-purple-400 cursor-pointer">
                                            {topic.title}
                                        </h3>
                                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
                                            <span>by {topic.author}</span>
                                            <span>{topic.replies} replies</span>
                                            <span>{topic.views} views</span>
                                            <span>{topic.lastActivity}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Community Stats */}
                <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 text-white">
                        <div className="text-3xl font-bold mb-2">2,847</div>
                        <div className="text-purple-100">Total Members</div>
                    </div>
                    <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl p-6 text-white">
                        <div className="text-3xl font-bold mb-2">1,234</div>
                        <div className="text-green-100">Total Topics</div>
                    </div>
                    <div className="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-xl p-6 text-white">
                        <div className="text-3xl font-bold mb-2">5,678</div>
                        <div className="text-yellow-100">Total Posts</div>
                    </div>
                    <div className="bg-gradient-to-r from-red-600 to-pink-600 rounded-xl p-6 text-white">
                        <div className="text-3xl font-bold mb-2">156</div>
                        <div className="text-red-100">Online Now</div>
                    </div>
                </div>
            </div>
        </div>
    );
}
