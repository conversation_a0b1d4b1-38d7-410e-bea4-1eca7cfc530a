'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function BlogsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const blogPosts = [
    {
      id: 1,
      title: "Etherchain AI Moves to Discord — Official Community Announcement",
      description: "Telegram has served us well during the early stages of the project, but as the ecosystem grows and more developers, validators, GPU providers, and community contributors come on board, we need a more organized, secure, and scalable space to communicate.",
      image: "https://images.ctfassets.net/86mqvksj9roh/4X8gar1c5yPHM71zn5D7le/4ea3847cf2a0bb2bc86748ee9f869d21/100.jpg",
      date: "July 4, 2025",
      slug: "etherchain-ai-moves-to-discord-official-community-announcement",
      author: "Admin",
      category: "Discord",
      featured: true
    },
    {
      id: 2,
      title: "Etherchain AI Completes Stage 15 — Launches Bonus Round to Accelerate Mainnet and Developer Expansion",
      description: "Etherchain AI is proud to announce the successful completion of Stage 15 of its ECAI token presale. As a key milestone in our roadmap, this stage",
      image: "https://images.ctfassets.net/86mqvksj9roh/2XP0vAJIIu32WmmIEURsXA/8720e4478267dd9fede4b2e2ec588c46/1_9ADMPnLR0-6GEZNnkD3czg.webp",
      date: "July 4, 2025",
      slug: "etherchain-ai-completes-stage-15-launches-bonus-round-to-accelerate-mainnet",
      author: "Admin",
      category: "Crypto"
    },
    {
      id: 3,
      title: "Etherchain AI Unleashes AIVM + PoI The Dawn of Permissionless Intelligence",
      description: "In a world drowning in closed APIs, censorship layers, and gate kept model access, Etherchain AI is ripping the muzzle off...",
      image: "https://images.ctfassets.net/86mqvksj9roh/30eNxwJWhC6uy8SQ3ZPa53/51d435b9bb12151d6b92b04342e6407b/1_7IfEVa9WG4v6dSG7CnIjeQ.webp",
      date: "July 4, 2025",
      slug: "etherchain-ai-unleashes-aivm-poi-the-dawn-of-permissionless-intelligence",
      author: "Admin",
      category: "AIVM"
    },
    {
      id: 4,
      title: "Etherchain Protocol AI Launches Massive Developer Grant Program to Accelerate Ecosystem Innovation",
      description: "As one of the most talked-about Layer 1 crypto presales this year, Etherchain Protocol AI is making a bold move to expand its...",
      image: "https://images.ctfassets.net/86mqvksj9roh/3rgphnzhToagW53SW5o5CR/fdbb54f6d661c16fdf37a68e7ac12268/1_z5VM523aYWa9KX5AR1V_Zw.webp",
      date: "July 4, 2025",
      slug: "etherchain-protocol-ai-launches-massive-developer-grant-program-to",
      author: "Admin",
      category: "Crypto"
    },
    {
      id: 5,
      title: "Etherchain AI Set to Launch This June— Get Ready for the Meme Coin Revolution",
      description: "We're excited to share a major milestone with the Etherchain AI community — Etherchain AI will officially be launching in June! While we're...",
      image: "https://images.ctfassets.net/86mqvksj9roh/Havhc7WQoGLSREjdhVbAd/0385516822e625b37b96a64124714667/1_e6j0dl-UkZiRwYx8lfRutg.webp",
      date: "July 4, 2025",
      slug: "etherchain-ai-set-to-launch-this-june-get-ready-for-the-meme-coin-revolution",
      author: "Admin",
      category: "Crypto"
    },
    {
      id: 7,
      title: "Etherchain Protocol AI Launches Highly Anticipated ECAI Token Presale",
      description: "The future of blockchain and artificial intelligence integration begins today as Etherchain Protocol AI officially announces the presale launch of its groundbreaking...",
      image: "https://images.ctfassets.net/86mqvksj9roh/71u9JRQ6RvhoumxjTH8D42/9e99ece6e2a8a97a484889f5dbb425ac/1_wijHPIDJr9PGb8sGFaX34Q.webp",
      date: "July 4, 2025",
      slug: "etherchain-protocol-ai-launches-highly-anticipated-ecai-token-presale",
      author: "Admin",
      category: "Crypto"
    }
  ];

  const categories = ['all', 'Discord', 'Telegram', 'Etherchain Launch', 'AIVM', 'Crypto'];
  const trendingSearches = ['Discord', 'Telegram', 'Etherchain Launch', 'AIVM', 'Crypto'];

  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = blogPosts.filter(post => !post.featured);
  const popularPosts = blogPosts.slice(0, 3);

  const filteredPosts = regularPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 pt-16">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-purple-900 via-purple-800 to-pink-900 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Latest <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">News</span>
            </h1>
            <p className="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
              Stay Updated with the Latest Insights from Etherchain AI
            </p>
            
            {/* Search Form */}
            <form className="max-w-2xl mx-auto mb-8">
              <div className="flex gap-4">
                <input
                  type="text"
                  placeholder="Search ..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent"
                />
                <button
                  type="submit"
                  className="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
                >
                  Search
                  <svg className="w-4 h-4 ml-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </div>
            </form>

            {/* Category Tags */}
            <div className="flex flex-wrap justify-center gap-3">
              <span className="text-gray-300 font-medium">Trending Searches:</span>
              {trendingSearches.map((tag) => (
                <Link
                  key={tag}
                  href={`/blogs/tags/${tag}`}
                  className="px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white hover:bg-white/20 transition-all duration-300 text-sm"
                >
                  {tag}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      {featuredPost && (
        <section className="py-20 bg-gray-50 dark:bg-gray-950">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                <div className="relative">
                  <img
                    src={featuredPost.image}
                    alt={featuredPost.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-sm font-medium px-4 py-2 rounded-full shadow-lg">
                      {featuredPost.date}
                    </span>
                  </div>
                </div>
                <div className="p-8 lg:p-12">
                  <div className="mb-4">
                    <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent font-semibold">
                      FEATURED
                    </span>
                  </div>
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-600 dark:text-gray-400 font-medium">
                      {featuredPost.author}
                    </span>
                  </div>
                  <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    <Link href={`/blog/${featuredPost.slug}`} className="hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-300">
                      {featuredPost.title}
                    </Link>
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                    {featuredPost.description}
                  </p>
                  <Link
                    href={`/blog/${featuredPost.slug}`}
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
                  >
                    Continue Reading
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Main Blog Content */}
      <section className="py-20 bg-gray-50 dark:bg-gray-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-8">
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Latest <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Blog</span>
                </h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {filteredPosts.slice(0, 2).map((post) => (
                  <article key={post.id} className="bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div className="relative">
                      <img
                        src={post.image}
                        alt={post.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                          {post.date}
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-6 h-6 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-2">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                          {post.author}
                        </span>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 line-clamp-2">
                        <Link href={`/blog/${post.slug}`} className="hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-300">
                          {post.title}
                        </Link>
                      </h4>
                      <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed line-clamp-3">
                        {post.description}
                      </p>
                    </div>
                  </article>
                ))}
              </div>

              {/* Remaining Posts */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8">
                {filteredPosts.slice(2).map((post) => (
                  <article key={post.id} className="bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div className="relative">
                      <img
                        src={post.image}
                        alt={post.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                          {post.date}
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-6 h-6 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-2">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                          {post.author}
                        </span>
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 line-clamp-2">
                        <Link href={`/blog/${post.slug}`} className="hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-300">
                          {post.title}
                        </Link>
                      </h4>
                      <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed line-clamp-3">
                        {post.description}
                      </p>
                    </div>
                  </article>
                ))}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-4">
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Most <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Popular</span>
                </h3>
              </div>
              
              <div className="space-y-6">
                {popularPosts.map((post) => (
                  <article key={post.id} className="bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div className="flex">
                      <div className="w-24 h-24 flex-shrink-0">
                        <img
                          src={post.image}
                          alt={post.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 p-4">
                        <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                          <Link href={`/blog/${post.slug}`} className="hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-300">
                            {post.title}
                          </Link>
                        </h4>
                        <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                          <span className="flex items-center mr-4">
                            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                            {post.author}
                          </span>
                          <span className="flex items-center">
                            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            4 days ago
                          </span>
                        </div>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>

          {/* View All Posts Button */}
          <div className="text-center mt-16">
            <Link
              href="/blog-archive"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 border border-purple-500 hover:border-purple-600"
            >
              View All Post
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
