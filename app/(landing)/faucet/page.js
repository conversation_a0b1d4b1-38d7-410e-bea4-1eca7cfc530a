'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faWallet,
  faCoins,
  faClock,
  faCheckCircle,
  faExclamationTriangle,
  faSpinner
} from '@fortawesome/free-solid-svg-icons';
import { faucetAddress, faucetAbi } from '@/context/AppKit';
import { useAppKitAccount, useAppKitNetworkCore } from '@reown/appkit/react';

export default function FaucetPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');
  const [claimed, setClaimed] = useState(false);
  const [lastClaimTime, setLastClaimTime] = useState(null);
  const { walletProvider } = useAppKitNetworkCore();
  const { address, isConnected, chainId } = useAppKitAccount();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!isConnected) {
      setError('Please connect your wallet');
      return;
    }

    if (!address.startsWith('0x') || address.length !== 42) {
      setError('Please enter a valid Ethereum address (0x...)');
      return;
    }

    setIsLoading(true);
    setError('');

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setIsSuccess(true);
      setLastClaimTime(new Date());
      setAddress('');
      
      // Reset success message after 5 seconds
      setTimeout(() => {
        setIsSuccess(false);
      }, 5000);
    }, 2000);
  };

  const canClaim = async () => {
    if (!isConnected) return false;
    const provier = new BrowserProvider(walletProvider);
    const contract = new Contract(faucetAddress[chainId], faucetAbi, provier);
    const claimed = await contract.claimed(address);
    setClaimed(claimed);
  };

  useEffect(() => {
    canClaim();
  }, [isConnected, chainId, address]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white  flex justify-center items-center">
      <div className="grid items-center mx-auto max-w-2xl gap-6 p-6 position-relative">
        {/* Header */}
        <h1 className="text-center font-semibold text-5xl">
          Etherchain{' '}
          <span className="bg-blue-600 text-white uppercase px-2 rounded">
            Faucet
          </span>
        </h1>

        {/* Main Form */}
        <div className="rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900/50 shadow-lg">
          <div className="p-6 space-y-4">
            <div className="flex items-center mb-4">
              <FontAwesomeIcon icon={faCoins} className="text-blue-600 dark:text-blue-400 text-xl mr-3" />
              <p className="text-xl font-semibold text-gray-900 dark:text-white">Get Test Tokens</p>
            </div>

            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-2 [&>li]:ml-4 list-disc">
              <li>Etherchain Testnet tokens can be claimed.</li>
              <li>Maximum 5 ETHAI tokens per Wallet.</li>
              <li>Tokens are sent directly to your wallet address.</li>
            </ul>

            <form className="space-y-4" onSubmit={handleSubmit}>
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center text-gray-900 dark:text-white" htmlFor="address">
                  <FontAwesomeIcon icon={faWallet} className="mr-2 text-gray-500 dark:text-gray-400" />
                  Wallet Address
                </label>
                <input
                  className="flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-3 py-2 text-base shadow-sm transition-colors placeholder:text-gray-500 dark:placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500 disabled:cursor-not-allowed disabled:opacity-50 text-gray-900 dark:text-white"
                  id="address"
                  placeholder="Enter Your Wallet Address (0x...)"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  disabled={isLoading || !canClaim()}
                />
              </div>

              {error && (
                <div className="flex items-center text-red-600 dark:text-red-400 text-sm">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                  {error}
                </div>
              )}

              {isSuccess && (
                <div className="flex items-center text-green-600 dark:text-green-400 text-sm">
                  <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
                  Success! 5 ETHAI tokens have been sent to your wallet.
                </div>
              )}

              {lastClaimTime && !canClaim() && (
                <div className="flex items-center text-yellow-600 dark:text-yellow-400 text-sm">
                  <FontAwesomeIcon icon={faClock} className="mr-2" />
                  You can claim again in {Math.ceil(24 - ((new Date() - lastClaimTime) / (1000 * 60 * 60)))} hours
                </div>
              )}

              <button
                className={`inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-blue-500 disabled:pointer-events-none disabled:opacity-50 h-10 rounded-md px-8 w-full ${
                  isLoading || !canClaim()
                    ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
                disabled={isLoading || !canClaim()}
                type="submit"
              >
                {isLoading ? (
                  <>
                    <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                    Processing...
                  </>
                                 ) : (
                   <>
                     <FontAwesomeIcon icon={faCoins} />
                     Send 5 ETHAI
                   </>
                 )}
              </button>
            </form>

            <p className="text-sm text-gray-600 dark:text-gray-400">
              You can use test ETHAI tokens to conduct testnet transactions, deploy smart contracts, or perform other on-chain activities.
            </p>
          </div>
        </div>

        {/* Additional Info */}
        <div className="grid md:grid-cols-2 gap-4 mt-6">
          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">How to Use</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Connect your wallet to the testnet</li>
              <li>• Enter your wallet address</li>
              <li>• Click "Send 2 ETHAI"</li>
              <li>• Wait for confirmation</li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Testnet Info</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Network: Etherchain Testnet</li>
              <li>• RPC URL: https://testnet.rpc.etherchain.com</li>
              <li>• Chain ID: 1337</li>
              <li>• Currency: ETHAI</li>
            </ul>
          </div>
        </div>

        {/* Animation Wrapper */}
        <div className="animation-wrapper">
          <div className="animation-dot"></div>
        </div>
      </div>

      <style jsx>{`
        .animation-wrapper {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
          z-index: -1;
        }

        .animation-dot {
          position: absolute;
          width: 4px;
          height: 4px;
          background: rgba(59, 130, 246, 0.5);
          border-radius: 50%;
          animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translateY(-100px) translateX(100px);
            opacity: 0;
          }
        }

        .animation-dot:nth-child(1) {
          top: 20%;
          left: 10%;
          animation-delay: 0s;
        }

        .animation-dot:nth-child(2) {
          top: 60%;
          left: 80%;
          animation-delay: 2s;
        }

        .animation-dot:nth-child(3) {
          top: 80%;
          left: 20%;
          animation-delay: 4s;
        }
      `}</style>
    </div>
  );
}
