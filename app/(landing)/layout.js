import { Inter } from "next/font/google";
import "../globals.css";
import Navbar from "../components/Navbar";
import { ThemeProvider } from "../components/ThemeProvider";
import Footer from "../components/Footer";
import { AppKit } from "@/context/AppKit";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata = {
  title: "Etherchain AI - Bridging AI with Blockchain",
  description: "Revolutionizing intelligence through decentralized innovation. Join the future of AI and blockchain with Etherchain Protocol.",
  icons: {
    icon: '/logo_alpha.png',
  },
};

export default function RootLayout({ children }) {
  // Check if children already contains a complete HTML structure
  // This happens when a nested layout provides its own html/body tags
  const childrenArray = Array.isArray(children) ? children : [children];
  const hasCompleteHTML = childrenArray.some(child => 
    child && typeof child === 'object' && child.type === 'html'
  );

  // If children already has complete HTML structure, return it directly
  if (hasCompleteHTML) {
    return children;
  }

  // Otherwise, wrap with root layout
  return (
    <html lang="en">
      <body className={`${inter.variable} bg-gray-50 dark:bg-gray-950`}>
        <ThemeProvider>
          <AppKit>
            <Navbar />
            {children}
            <Footer />
          </AppKit>
        </ThemeProvider>
      </body>
    </html>
  );
}
