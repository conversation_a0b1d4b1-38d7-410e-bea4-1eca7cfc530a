'use client'

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Divider from '../../../components/Divider';

export default function ImportTokenPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      {/* Header Section */}
      <div className="relative overflow-hidden bg-white/90 dark:bg-gray-950/90 backdrop-blur-md">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
         
          {/* Page Title */}
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Import Token
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Import your Etherchain AI Tokens to your MetaMask and TrustWallet
            </p>
          </div>
        </div>
      </div>

      <Divider />

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="space-y-8">
          {/* Warning Section */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Important Security Notice
                </h3>
                <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <p>
                    Always verify the contract address before importing tokens. Only import tokens from trusted sources. 
                    Etherchain AI tokens should only be imported using the official contract addresses provided in our documentation.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* MetaMask Section */}
          <section>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <svg className="w-6 h-6 mr-3 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M21.49 2L13.5 9.5L15.5 4.5L21.49 2Z"/>
                <path d="M2.5 2L10.5 9.5L8.5 4.5L2.5 2Z"/>
                <path d="M2.5 22L10.5 14.5L8.5 19.5L2.5 22Z"/>
                <path d="M21.49 22L13.5 14.5L15.5 19.5L21.49 22Z"/>
              </svg>
              Import to MetaMask
            </h3>
            
            <Link 
              href="https://support.metamask.io/de/manage-crypto/portfolio/how-to-import-a-token-in-metamask-portfolio/"
              className="group flex flex-row justify-between items-center gap-4 ring-1 ring-gray-200 dark:ring-gray-700 rounded-2xl px-5 py-4 transition-all duration-300 hover:ring-purple-500 hover:shadow-lg bg-white dark:bg-gray-800"
            >
              <span className="flex flex-col flex-1">
                <span className="text-black dark:text-white text-base font-medium transition-colors group-hover:text-purple-600 dark:group-hover:text-purple-400">
                  MetaMask
                </span>
              </span>
              <svg className="w-4 h-4 text-gray-400 transition-all group-hover:translate-x-1 group-hover:text-purple-600 dark:group-hover:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </Link>
            
            <p className="mt-3 text-gray-600 dark:text-gray-400">
              View this page to import Etherchain AI Token to your MetaMask Wallet
            </p>
          </section>

          <hr className="border-gray-200 dark:border-gray-700" />

          {/* TrustWallet Section */}
          <section>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <svg className="w-6 h-6 mr-3 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L2 7L12 12L22 7L12 2Z"/>
                <path d="M2 17L12 22L22 17"/>
                <path d="M2 12L12 17L22 12"/>
              </svg>
              Import to TrustWallet
            </h3>
            
            <Link 
              href="https://trustwallet.com/de/blog/how-to-add-a-custom-token-using-trust-wallet"
              className="group flex flex-row justify-between items-center gap-4 ring-1 ring-gray-200 dark:ring-gray-700 rounded-2xl px-5 py-4 transition-all duration-300 hover:ring-purple-500 hover:shadow-lg bg-white dark:bg-gray-800"
            >
              <span className="flex flex-col flex-1">
                <span className="text-black dark:text-white text-base font-medium transition-colors group-hover:text-purple-600 dark:group-hover:text-purple-400">
                  TrustWallet
                </span>
              </span>
              <svg className="w-4 h-4 text-gray-400 transition-all group-hover:translate-x-1 group-hover:text-purple-600 dark:group-hover:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </Link>
            
            <p className="mt-3 text-gray-600 dark:text-gray-400">
              View this page to import Etherchain AI Token to your TrustWallet
            </p>
          </section>

          <hr className="border-gray-200 dark:border-gray-700" />

          {/* Other Wallets Section */}
          <section>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <svg className="w-6 h-6 mr-3 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              Other Wallets
            </h3>
            
            <Link 
              href="https://help.crypto.com/en/articles/5755942-tokens-import"
              className="group flex flex-row justify-between items-center gap-4 ring-1 ring-gray-200 dark:ring-gray-700 rounded-2xl px-5 py-4 transition-all duration-300 hover:ring-purple-500 hover:shadow-lg bg-white dark:bg-gray-800"
            >
              <span className="flex flex-col flex-1">
                <span className="text-black dark:text-white text-base font-medium transition-colors group-hover:text-purple-600 dark:group-hover:text-purple-400">
                  Other Wallets
                </span>
              </span>
              <svg className="w-4 h-4 text-gray-400 transition-all group-hover:translate-x-1 group-hover:text-purple-600 dark:group-hover:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </Link>
            
            <p className="mt-3 text-gray-600 dark:text-gray-400">
              View this page to find guides from other DeFi wallets on how to import Etherchain Token.
            </p>
          </section>
        </div>

        {/* Navigation */}
        <div className="grid grid-cols-2 md:flex-row mt-12 gap-4">
          <Link 
            href="/how-to-buy/get-ethereum"
            className="group text-sm p-4 flex gap-4 flex-1 flex-row-reverse items-center pl-4 border border-gray-200 dark:border-gray-700 rounded-2xl hover:border-purple-500 hover:shadow-lg bg-white dark:bg-gray-800 transition-all duration-300"
          >
            <span className="flex flex-col flex-1 text-right">
              <span className="text-xs text-gray-500 dark:text-gray-400">Previous</span>
              <span className="text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 font-medium">
                Get Ethereum
              </span>
            </span>
            <svg className="w-4 h-4 text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>

        {/* Last Updated */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Last updated <time dateTime="2024-12-09T01:47:27.866Z">7 months ago</time>
          </p>
        </div>
      </div>
    </div>
  );
}
