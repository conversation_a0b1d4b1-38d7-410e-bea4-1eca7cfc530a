'use client'

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function GetEthereumPage() {
  return (
    <div className="min-h-screen from-gray-50 dark:bg-gray-950">
      {/* Header Section */}
      <div className="relative overflow-hidden bg-white dark:bg-gray-950 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

          {/* Page Header */}
          <div className="text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Get <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-transparent bg-clip-text">Ethereum</span>
            </h1>
            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto lg:mx-0">
              Ethereum (ETH) is essential for interacting with many blockchain networks, including bridging assets to Etherchain AI.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 bg-gray-50 dark:bg-gray-950">
        {/* Warning Box */}
        <div className="mb-8 p-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                Important Notice
              </h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                You cannot use any exchange wallet like Binance, Coinbase, Kraken, or any other centralized exchange. 
                You will need to use a self custody wallet like MetaMask or TrustWallet.
              </p>
            </div>
          </div>
        </div>

        {/* Method 1: Buy with Card */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-transparent bg-clip-text">1.</span>
            <span className="ml-3">Buy Ethereum with a Debit or Credit Card</span>
          </h2>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Several platforms allow you to purchase Ethereum directly using a credit or debit card. 
            Here are some trusted options. You can purchase Ethereum via these exchanges but you will need to 
            transfer it to a DEFI wallet like MetaMask or TrustWallet.
          </p>

          <div className="grid gap-6">
            {/* Coinbase */}
            <div className="bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Coinbase</h3>
                <a 
                  href="https://www.coinbase.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200"
                >
                  Visit Site
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </a>
              </div>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  User-friendly for beginners
                </li>
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Supports card payments and bank transfers
                </li>
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Available in most regions worldwide
                </li>
              </ul>
            </div>

            {/* Binance */}
            <div className="bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Binance</h3>
                <a 
                  href="https://www.binance.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200"
                >
                  Visit Site
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </a>
              </div>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Offers a wide range of payment options, including credit cards
                </li>
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Competitive fees and fast processing
                </li>
              </ul>
            </div>

            {/* Crypto.com */}
            <div className="bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Crypto.com</h3>
                <a 
                  href="https://www.crypto.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200"
                >
                  Visit Site
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </a>
              </div>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Provides card purchases with minimal setup
                </li>
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Includes a mobile app for on-the-go access
                </li>
              </ul>
            </div>

            {/* Kraken */}
            <div className="bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Kraken</h3>
                <a 
                  href="https://www.kraken.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200"
                >
                  Visit Site
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </a>
              </div>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Reliable platform for buying ETH with cards or wire transfers
                </li>
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Offers detailed tutorials for beginners
                </li>
              </ul>
            </div>

            {/* MoonPay */}
            <div className="bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">MoonPay</h3>
                <a 
                  href="https://www.moonpay.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200"
                >
                  Visit Site
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </a>
              </div>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Simple and intuitive interface
                </li>
                <li className="flex items-center">
                  <svg className="w-4 h-4 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  No need to set up a full exchange account
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Method 2: Bridge from Other Networks */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-transparent bg-clip-text">2.</span>
            <span className="ml-3">Transfer Ethereum from Other Networks</span>
          </h2>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            If you already own cryptocurrency on another blockchain (like Binance Smart Chain, Polygon, or Avalanche), 
            you can bridge those assets to the Ethereum network. Here's how:
          </p>

          <div className="bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Use a Trusted Bridge</h3>
            
            <div className="grid gap-4">
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Binance Bridge</h4>
                <p className="text-gray-600 dark:text-gray-300">Transfer assets from Binance Smart Chain to Ethereum.</p>
              </div>
              
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Multichain</h4>
                <p className="text-gray-600 dark:text-gray-300">Connect a variety of networks to Ethereum quickly and securely.</p>
              </div>
              
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Portal Bridge</h4>
                <p className="text-gray-600 dark:text-gray-300">A decentralized bridge that supports several blockchains.</p>
              </div>
              
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Hop Protocol</h4>
                <p className="text-gray-600 dark:text-gray-300">Ideal for Layer 2 networks like Polygon and Arbitrum.</p>
              </div>
            </div>

            <div className="mt-6">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Steps to Bridge</h4>
              <ol className="space-y-3">
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mr-3 mt-0.5">1</span>
                  <span className="text-gray-600 dark:text-gray-300">Connect your crypto wallet (MetaMask, Trust Wallet, etc.) to the bridging platform.</span>
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mr-3 mt-0.5">2</span>
                  <span className="text-gray-600 dark:text-gray-300">Select the source network (e.g., Binance Smart Chain) and destination (Ethereum).</span>
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mr-3 mt-0.5">3</span>
                  <span className="text-gray-600 dark:text-gray-300">Confirm the transaction and pay any associated fees.</span>
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mr-3 mt-0.5">4</span>
                  <span className="text-gray-600 dark:text-gray-300">Wait for the bridge to process your request (usually a few minutes).</span>
                </li>
              </ol>
            </div>
          </div>
        </section>

        {/* Method 3: Secure Your Ethereum */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-transparent bg-clip-text">3.</span>
            <span className="ml-3">Secure Your Ethereum</span>
          </h2>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Once you've purchased or bridged Ethereum, ensure it's stored safely:
          </p>

          <div className="grid gap-6">
            <div className="bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Non-Custodial Wallets</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Use wallets like MetaMask, Trust Wallet, or Ledger for full control over your funds.
              </p>
              <div className="flex flex-wrap gap-3">
                <a href="https://metamask.io" target="_blank" rel="noopener noreferrer" className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                  MetaMask
                </a>
                <a href="https://trustwallet.com" target="_blank" rel="noopener noreferrer" className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                  Trust Wallet
                </a>
                <a href="https://www.ledger.com" target="_blank" rel="noopener noreferrer" className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                  Ledger
                </a>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Custodial Options</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Platforms like Coinbase or Binance offer wallet services but retain control over your keys.
                <span className="block mt-2 font-semibold text-red-600 dark:text-red-400">
                  (YOU CANNOT USE THESE FOR PRESALE PURCHASES)
                </span>
              </p>
            </div>
          </div>
        </section>

        {/* Method 4: Use Ethereum on Etherchain AI */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-transparent bg-clip-text">4.</span>
            <span className="ml-3">Use Ethereum on Etherchain AI</span>
          </h2>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            After acquiring Ethereum, you can use it to:
          </p>

          <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-6 text-white">
            <ul className="space-y-3">
              <li className="flex items-center">
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Interact with the Etherchain AI ecosystem
              </li>
              <li className="flex items-center">
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Pay for bridging fees to bring assets into Etherchain's network
              </li>
              <li className="flex items-center">
                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Stake or swap it for Etherchain Tokens (ECAI) once connected to the Etherchain ecosystem
              </li>
            </ul>
          </div>

          <p className="text-center mt-6 text-lg text-gray-600 dark:text-gray-300">
            Start your Ethereum journey today and unlock the endless possibilities of decentralized AI with Etherchain AI!
          </p>
        </section>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row gap-4 mt-12">
          <Link 
            href="/how-to-buy/create-wallet"
            className="flex-1 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Previous</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400">
                  Create A Wallet
                </p>
              </div>
              <svg className="w-6 h-6 text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </div>
          </Link>
          
          <Link 
            href="/how-to-buy/import-token"
            className="flex-1 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group"
          >
            <div className="flex items-center justify-between">
              <svg className="w-6 h-6 text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
              </svg>
              <div className="text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">Next</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400">
                  Import Token
                </p>
              </div>
            </div>
          </Link>
        </div>

        {/* Last Updated */}
        <div className="text-center mt-8 text-sm text-gray-500 dark:text-gray-400">
          Last updated 7 months ago
        </div>
      </div>
    </div>
  );
}
