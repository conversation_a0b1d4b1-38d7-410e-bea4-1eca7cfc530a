'use client';

import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faCoins,
  faShare,
  faChartLine,
  faGift,
  faTrophy,
  faRocket,
  faShieldAlt
} from '@fortawesome/free-solid-svg-icons';
import GradientButton from '../../components/GradientButton';
import { BigNumber } from 'bignumber.js';
import { rpcUrls, presaleAddr, presaleAbi } from '../../../context/AppKit';
import { useAppKitNetworkCore } from '@reown/appkit/react';
import { JsonRpcProvider, Contract } from 'ethers';

export default function AffiliatePage() {
  const {chainId} = useAppKitNetworkCore();
  const [activeFAQ, setActiveFAQ] = useState(1);
  const [leaderboard, setLeaderboard] = useState([]);

  const toggleFAQ = (faqId) => {
    setActiveFAQ(activeFAQ === faqId ? null : faqId);
  };

  const prices = [
    25000,
    18000,
    13000,
    10000,
    8000,
    7000,
    6000,
    5000,
    4000,
    4000,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0 // Platz 11-20
  ]

  const [leaderboardData, setLeaderboardData] = useState([
    {
      address: "******************************************",
      referrals: 156,
      earnings: 2340.50,
    },
    {
      address: "******************************************",
      referrals: 142,
      earnings: 2130.00,
    },
    {
      address: "******************************************",
      referrals: 128,
      earnings: 1920.75,
    },
    {
      address: "******************************************",
      referrals: 115,
      earnings: 1725.25,
    },
    {
      address: "0x1111111111111111111111111111111111111111",
      referrals: 98,
      earnings: 1470.00,
    },
    {
      address: "0x2222222222222222222222222222222222222222",
      referrals: 87,
      earnings: 1305.50,
    },
    {
      address: "0x3333333333333333333333333333333333333333",
      referrals: 76,
      earnings: 1140.00,
    },
    {
      address: "0x4444444444444444444444444444444444444444",
      referrals: 65,
      earnings: 975.25,
    },
    {
      address: "0x5555555555555555555555555555555555555555",
      referrals: 54,
      earnings: 810.75,
    },
    {
      address: "0x6666666666666666666666666666666666666666",
      referrals: 43,
      earnings: 645.00,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    },
    {
      address: "0x0000000000000000000000000000000000000000",
      referrals: null,
      earnings: null,
    }
  ]);

  const truncateAddress = (address) => {
    return `${address.slice(0, 5)}...${address.slice(-4)}`;
  };

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return "🥇";
      case 2:
        return "🥈";
      case 3:
        return "🥉";
      default:
        return `#${rank}`;
    }
  };

  const features = [
    {
      icon: faCoins,
      title: "15% Commission",
      description: "Earn 15% on every purchase made by your referrals"
    },
    {
      icon: faShare,
      title: "Easy Sharing",
      description: "Share your unique referral link with friends and family"
    },
    {
      icon: faChartLine,
      title: "Real-time Tracking",
      description: "Monitor your earnings and referral progress in real-time"
    },
    {
      icon: faGift,
      title: "Instant Rewards",
      description: "Receive your commissions instantly to your wallet"
    }
  ];

  const benefits = [
    {
      icon: faTrophy,
      title: "Exclusive Rewards",
      description: "Unlock special rewards and bonuses for top performers"
    },
    {
      icon: faRocket,
      title: "Early Access",
      description: "Get early access to new features and presales"
    },
    {
      icon: faShieldAlt,
      title: "Secure Payments",
      description: "All payments are secured through blockchain technology"
    },
    {
      icon: faUsers,
      title: "Community",
      description: "Join our ambassador community and grow together"
    }
  ];

  const createLeaderboard = async () => {
    const provider = new JsonRpcProvider(rpcUrls[chainId], chainId);
    const presaleContract = new Contract(presaleAddr[chainId], presaleAbi, provider);
    const participants = await presaleContract.getPresaleUsers();

    const tempLeaderboard = [];

    for(let participant of participants) {
      const refCount = await presaleContract.refCount(participant);
      const refEarnings = await presaleContract.refEarnings(participant);

      const tempUser = {
        address: participant,
        referrals: parseInt(new BigNumber(refCount).toString()),
        earnings: parseFloat(new BigNumber(refEarnings).shiftedBy(-18).toString()),
        isVacant: false,
      }

      tempLeaderboard.push(tempUser);
    }
      tempLeaderboard.sort((a, b) => b.referrals - a.referrals);

      if(tempLeaderboard.length < 20) {
        for(let i = tempLeaderboard.length; i < 20; i++) {
          tempLeaderboard.push({
            address: "0x0000000000000000000000000000000000000000",
            referrals: null,
            earnings: null,
            isVacant: true,
          });
        }
      }
  
      const leadersData = tempLeaderboard.slice(0, 19);
      
      setLeaderboardData(leadersData);
  }

  useEffect(() => {
    createLeaderboard();
  }, [chainId]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-white ">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 from-20% via-blue-600 via-50% to-indigo-700 to-80% dark:from-purple-700 dark:via-blue-700 dark:to-indigo-800">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <div className="flex justify-center mb-6">
              <div className="bg-gradient-to-r from-purple-400 to-pink-400 p-1 rounded-full">
                <div className="bg-white dark:bg-gray-900 px-6 py-2 rounded-full">
                  <span className="text-purple-600 dark:text-purple-400 font-bold text-sm">🎯 AFFILIATE PROGRAM 🎯</span>
                </div>
              </div>
            </div>
            
            <h3 className="text-xl font-semibold mb-4 text-purple-100 dark:text-purple-200">
              Become an Ambassador
            </h3>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-8 text-white">
              Earn 15% by Referring Friends to 
              <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                Etherchain
              </span>
            </h1>
            
            <p className="text-xl text-purple-100 dark:text-purple-200 mb-8 max-w-3xl mx-auto">
              Join our affiliate program and earn generous commissions for every friend you bring to the Etherchain ecosystem.
            </p>

            <a href="/?showref=true">
              <button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-8 rounded-xl text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                <FontAwesomeIcon icon={faShare} className="mr-2" />
                GET MY REFERRAL LINK
              </button>
            </a>
          </div>
        </div>
      </div>

      {/* How to Start Earning Section */}
      <div className="py-20 bg-gray-50 dark:bg-gray-950">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">How to Start Earning</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Follow these simple steps to start earning commissions with Etherchain
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-3 gap-8">
              {/* Step 1 */}
              <div className="bg-white dark:bg-gray-900 rounded-xl p-8 border border-gray-200 dark:border-gray-700 hover:border-purple-500 transition-all duration-300 group shadow-lg relative">
                <div className="absolute -top-4 left-8">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    1
                  </div>
                </div>
                <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <FontAwesomeIcon icon={faShieldAlt} className="text-3xl text-white" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-900 dark:text-white">Connect Your Wallet</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Securely connect your crypto wallet to activate your referral dashboard.
                </p>
              </div>

              {/* Step 2 */}
              <div className="bg-white dark:bg-gray-900 rounded-xl p-8 border border-gray-200 dark:border-gray-700 hover:border-purple-500 transition-all duration-300 group shadow-lg relative">
                <div className="absolute -top-4 left-8">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    2
                  </div>
                </div>
                <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <FontAwesomeIcon icon={faShare} className="text-3xl text-white" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-900 dark:text-white">Share Your Unique Link</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Copy your personal referral link and share it with your friends, followers, or community.
                </p>
              </div>

              {/* Step 3 */}
              <div className="bg-white dark:bg-gray-900 rounded-xl p-8 border border-gray-200 dark:border-gray-700 hover:border-purple-500 transition-all duration-300 group shadow-lg relative">
                <div className="absolute -top-4 left-8">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    3
                  </div>
                </div>
                <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <FontAwesomeIcon icon={faCoins} className="text-3xl text-white" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-900 dark:text-white">Get Paid Instantly</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Earn 15% commission per referral and receive payments directly to your wallet.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

             {/* Leaderboard Section */}
       <div className="py-20 bg-gray-50 dark:bg-gray-950 relative overflow-hidden">
         {/* Animated Background Elements */}
         <div className="absolute inset-0 overflow-hidden w-full">
           {/* Floating Coins */}
           <div className="absolute top-10 left-10 w-8 h-8 bg-yellow-400 rounded-full animate-bounce opacity-20" style={{animationDelay: '0s', animationDuration: '3s'}}></div>
           <div className="absolute top-20 right-20 w-6 h-6 bg-yellow-400 rounded-full animate-bounce opacity-20" style={{animationDelay: '1s', animationDuration: '2.5s'}}></div>
           <div className="absolute bottom-20 left-1/4 w-4 h-4 bg-yellow-400 rounded-full animate-bounce opacity-20" style={{animationDelay: '2s', animationDuration: '3.5s'}}></div>
           <div className="absolute top-1/3 right-1/3 w-5 h-5 bg-yellow-400 rounded-full animate-bounce opacity-20" style={{animationDelay: '0.5s', animationDuration: '2.8s'}}></div>
           
           {/* Floating Trophies */}
           <div className="absolute top-1/4 left-1/3 text-2xl animate-bounce opacity-10" style={{animationDelay: '1.5s', animationDuration: '4s'}}>🏆</div>
           <div className="absolute bottom-1/4 right-1/4 text-xl animate-bounce opacity-10" style={{animationDelay: '2.5s', animationDuration: '3.2s'}}>🥇</div>
           <div className="absolute top-1/2 left-1/6 text-lg animate-bounce opacity-10" style={{animationDelay: '0.8s', animationDuration: '3.8s'}}>🥈</div>
           
           {/* Floating Stars */}
           <div className="absolute top-16 left-1/2 text-lg animate-pulse opacity-15" style={{animationDelay: '0s', animationDuration: '2s'}}>⭐</div>
           <div className="absolute bottom-16 right-1/6 text-sm animate-pulse opacity-15" style={{animationDelay: '1s', animationDuration: '2.5s'}}>⭐</div>
           <div className="absolute top-1/3 right-1/6 text-base animate-pulse opacity-15" style={{animationDelay: '2s', animationDuration: '1.8s'}}>⭐</div>
           
           {/* Floating Charts */}
           <div className="absolute top-1/2 right-1/3 text-xl animate-pulse opacity-10" style={{animationDelay: '0.5s', animationDuration: '3s'}}>📈</div>
           <div className="absolute bottom-1/3 left-1/2 text-lg animate-pulse opacity-10" style={{animationDelay: '1.8s', animationDuration: '2.2s'}}>📊</div>
           
           {/* Gradient Orbs */}
           <div className="absolute top-1/4 right-1/4 w-32 h-32 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full animate-pulse blur-xl"></div>
           <div className="absolute bottom-1/4 left-1/4 w-24 h-24 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full animate-pulse blur-xl" style={{animationDelay: '1s'}}></div>
           <div className="absolute top-1/2 left-1/2 w-20 h-20 bg-gradient-to-r from-pink-400/10 to-yellow-400/10 rounded-full animate-pulse blur-xl" style={{animationDelay: '2s'}}></div>
           
           {/* Moving Lines */}
           <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-purple-400/20 to-transparent animate-pulse"></div>
           <div className="absolute bottom-0 right-0 w-full h-1 bg-gradient-to-r from-transparent via-pink-400/20 to-transparent animate-pulse" style={{animationDelay: '1s'}}></div>
           
           {/* Falling Stars */}
           <div className="absolute -top-4 left-[5%] text-lg text-yellow-400 opacity-60" style={{
             animation: 'fallStar 8s linear infinite',
             animationDelay: '0s'
           }}>⭐</div>
           <div className="absolute -top-8 left-[15%] text-sm text-yellow-300 opacity-50" style={{
             animation: 'fallStar 10s linear infinite',
             animationDelay: '2s'
           }}>⭐</div>
           <div className="absolute -top-12 left-[25%] text-base text-yellow-500 opacity-70" style={{
             animation: 'fallStar 12s linear infinite',
             animationDelay: '4s'
           }}>⭐</div>
           <div className="absolute -top-6 left-[35%] text-lg text-yellow-400 opacity-60" style={{
             animation: 'fallStar 9s linear infinite',
             animationDelay: '6s'
           }}>⭐</div>
           <div className="absolute -top-10 left-[45%] text-sm text-yellow-300 opacity-50" style={{
             animation: 'fallStar 11s linear infinite',
             animationDelay: '1s'
           }}>⭐</div>
           <div className="absolute -top-14 left-[55%] text-base text-yellow-500 opacity-70" style={{
             animation: 'fallStar 13s linear infinite',
             animationDelay: '3s'
           }}>⭐</div>
           <div className="absolute -top-16 left-[65%] text-lg text-yellow-400 opacity-60" style={{
             animation: 'fallStar 7s linear infinite',
             animationDelay: '5s'
           }}>⭐</div>
           <div className="absolute -top-20 left-[75%] text-sm text-yellow-300 opacity-50" style={{
             animation: 'fallStar 14s linear infinite',
             animationDelay: '7s'
           }}>⭐</div>
           <div className="absolute -top-4 left-[85%] text-lg text-yellow-400 opacity-60" style={{
             animation: 'fallStar 8.5s linear infinite',
             animationDelay: '1.5s'
           }}>⭐</div>
           <div className="absolute -top-8 left-[95%] text-sm text-yellow-300 opacity-50" style={{
             animation: 'fallStar 11.5s linear infinite',
             animationDelay: '3.5s'
           }}>⭐</div>
           <div className="absolute -top-12 left-[10%] text-base text-yellow-500 opacity-70" style={{
             animation: 'fallStar 9.5s linear infinite',
             animationDelay: '5.5s'
           }}>⭐</div>
           <div className="absolute -top-6 left-[20%] text-lg text-yellow-400 opacity-60" style={{
             animation: 'fallStar 10.5s linear infinite',
             animationDelay: '0.5s'
           }}>⭐</div>
           <div className="absolute -top-10 left-[30%] text-sm text-yellow-300 opacity-50" style={{
             animation: 'fallStar 12.5s linear infinite',
             animationDelay: '2.5s'
           }}>⭐</div>
           <div className="absolute -top-14 left-[40%] text-base text-yellow-500 opacity-70" style={{
             animation: 'fallStar 8.8s linear infinite',
             animationDelay: '4.5s'
           }}>⭐</div>
           <div className="absolute -top-16 left-[50%] text-lg text-yellow-400 opacity-60" style={{
             animation: 'fallStar 11.8s linear infinite',
             animationDelay: '6.5s'
           }}>⭐</div>
           <div className="absolute -top-4 left-[60%] text-base text-yellow-500 opacity-70" style={{
             animation: 'fallStar 9.2s linear infinite',
             animationDelay: '1.2s'
           }}>⭐</div>
           <div className="absolute -top-8 left-[70%] text-lg text-yellow-400 opacity-60" style={{
             animation: 'fallStar 10.8s linear infinite',
             animationDelay: '3.8s'
           }}>⭐</div>
           <div className="absolute -top-12 left-[80%] text-sm text-yellow-300 opacity-50" style={{
             animation: 'fallStar 12.2s linear infinite',
             animationDelay: '5.8s'
           }}>⭐</div>
           <div className="absolute -top-6 left-[90%] text-base text-yellow-500 opacity-70" style={{
             animation: 'fallStar 8.2s linear infinite',
             animationDelay: '7.2s'
           }}>⭐</div>
                   </div>
          <div className="container mx-auto px-4 relative z-10">
           <div className="text-center mb-16">
             <h2 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">Leaderboard</h2>
             <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
               See how you stack up against other affiliates
             </p>
           </div>
           
           <div className="max-w-7xl lg:max-w-4xl mx-auto overflow-x-scroll lg:overflow-x-hidden">
             <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
               {/* Header */}
               <div className="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4">
                 <div className="grid grid-cols-5 gap-4 text-white font-semibold">
                   <div className="text-xs">Rank</div>
                   <div className="text-xs">Address</div>
                   <div className="text-center text-xs">Referrals</div>
                   <div className="text-center text-xs">Earnings</div>
                   <div className="text-right text-xs">Prize</div>
                 </div>
               </div>
               
               {/* Leaderboard Items */}
               <div className="divide-y divide-gray-200 dark:divide-gray-700 overflow-x-auto">
                 {leaderboardData.map((leader, index) => (
                   <div 
                     key={index}
                     className={`px-4 py-4 transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800`}
                   >
                     <div className="grid grid-cols-5 gap-2 min-w-[600px] lg:min-w-0 lg:gap-4 items-center">
                       {/* Rank */}
                       <div className="flex items-center">
                         <span className="text-2xl">{getRankIcon(index + 1)}</span>
                       </div>
                       
                       {/* Address */}
                       <div className="flex items-center">
                         <div>
                           <div className={`font-mono text-sm ${
                             leader.isVacant 
                               ? 'text-gray-400 dark:text-gray-500 italic' 
                               : 'text-gray-900 dark:text-white'
                           }`}>
                             {truncateAddress(leader.address)}
                           </div>
                         </div>
                       </div>
                       
                       {/* Referrals */}
                       <div className="text-center">
                         <div className={`text-lg font-bold ${
                           leader.isVacant 
                             ? 'text-gray-400 dark:text-gray-500' 
                             : 'text-gray-900 dark:text-white'
                         }`}>
                           {leader.referrals === null ? '-' : leader.referrals}
                         </div>
                         <div className="text-xs text-gray-500 dark:text-gray-400">
                           referrals
                         </div>
                       </div>

                       {/* Earnings */}
                       <div className="text-center">
                         <div className={`text-lg font-bold ${
                           leader.isVacant 
                             ? 'text-gray-400 dark:text-gray-500' 
                             : 'text-green-600 dark:text-green-400'
                         }`}>
                           {leader.earnings === null ? '-' : `$${leader.earnings.toFixed(2).toLocaleString()}`}
                         </div>
                         <div className="text-xs text-gray-500 dark:text-gray-400">
                           earnings
                         </div>
                       </div>

                       {/* Prize */}
                       <div className="text-right mr-4">
                         <div className={`text-lg font-bold ${
                           leader.isVacant
                             ? 'text-gray-400 dark:text-gray-500' 
                             : 'text-green-600 dark:text-green-400'
                         }`}>
                           ${prices[index].toLocaleString()}
                         </div>
                         <div className="text-xs text-gray-500 dark:text-gray-400">
                           prize
                         </div>
                       </div>
                     </div>
                   </div>
                 ))}
               </div>
               
               {/* Footer */}
               <div className="bg-gray-50 dark:bg-gray-800 px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                 <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                   Top 10 affiliates this month • Updated every hour
                 </div>
               </div>
             </div>
           </div>
         </div>
       </div>

        {/* FAQ Section */}
        <div className="py-20 bg-white dark:bg-gray-950">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">Frequently Asked Questions</h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Everything you need to know about the Etherchain affiliate program
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden">
                {/* FAQ Item 1 */}
                <div className="border-b border-gray-200 dark:border-gray-700">
                  <button 
                    className="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200"
                    onClick={() => toggleFAQ(1)}
                  >
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      How do I earn from the Etherchain affiliate program?
                    </span>
                    <span className="text-2xl font-bold text-purple-600 dark:text-purple-400 transition-transform duration-200">
                      {activeFAQ === 1 ? '−' : '+'}
                    </span>
                  </button>
                  <div className={`overflow-hidden transition-all duration-300 ${
                    activeFAQ === 1 ? 'max-h-96' : 'max-h-0'
                  }`}>
                    <div className="p-6 pt-0">
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        As an Etherchain affiliate, you'll earn a solid 15% commission paid out in ETH, USDT, and USDC from every presale purchase made through your referral link. We sweeten the deal even more by offering monetary rewards to our top 10 affiliates each month, with a total reward pool of $50,000. The top performer alone claims an impressive $15,000!
                      </p>
                    </div>
                  </div>
                </div>

                {/* FAQ Item 2 */}
                <div className="border-b border-gray-200 dark:border-gray-700">
                  <button 
                    className="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200"
                    onClick={() => toggleFAQ(2)}
                  >
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      When and how do I receive my commissions?
                    </span>
                    <span className="text-2xl font-bold text-purple-600 dark:text-purple-400 transition-transform duration-200">
                      {activeFAQ === 2 ? '−' : '+'}
                    </span>
                  </button>
                  <div className={`overflow-hidden transition-all duration-300 ${
                    activeFAQ === 2 ? 'max-h-96' : 'max-h-0'
                  }`}>
                    <div className="p-6 pt-0">
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        Receive your earnings (Referral Bonuses) instantly for each referral. The leaderboard bonus will be paid out after the end of each month. You'll receive commissions in ETH, USDT, and USDC paid directly to your wallet.
                      </p>
                    </div>
                  </div>
                </div>

                {/* FAQ Item 3 */}
                <div className="border-b border-gray-200 dark:border-gray-700">
                  <button 
                    className="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200"
                    onClick={() => toggleFAQ(3)}
                  >
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      Is there a limit to the number of referrals I can make?
                    </span>
                    <span className="text-2xl font-bold text-purple-600 dark:text-purple-400 transition-transform duration-200">
                      {activeFAQ === 3 ? '−' : '+'}
                    </span>
                  </button>
                  <div className={`overflow-hidden transition-all duration-300 ${
                    activeFAQ === 3 ? 'max-h-96' : 'max-h-0'
                  }`}>
                    <div className="p-6 pt-0">
                      <div className="text-gray-600 dark:text-gray-400 leading-relaxed space-y-3">
                        <p>Absolutely not - the sky's the limit!</p>
                        <p>We encourage you to share your referral link with as many people as you're connected with.</p>
                        <p>Whether it's friends, family, or colleagues, the more you refer, the more potential rewards you can earn.</p>
                        <p>It's important to note, however, that using your referral link for your own purchases is not permitted.</p>
                        <p>Doing so would result in the forfeiture of any commissions earned from such actions.</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* FAQ Item 4 */}
                <div className="border-b border-gray-200 dark:border-gray-700">
                  <button 
                    className="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200"
                    onClick={() => toggleFAQ(4)}
                  >
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      How do I track my referrals?
                    </span>
                    <span className="text-2xl font-bold text-purple-600 dark:text-purple-400 transition-transform duration-200">
                      {activeFAQ === 4 ? '−' : '+'}
                    </span>
                  </button>
                  <div className={`overflow-hidden transition-all duration-300 ${
                    activeFAQ === 4 ? 'max-h-96' : 'max-h-0'
                  }`}>
                    <div className="p-6 pt-0">
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        You're able to track your referrals in our referral dashboard. Connect your wallet to access real-time statistics and earnings.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* CTA Section */}
      <div className="py-20 bg-gray-50 dark:bg-gray-950">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6 text-gray-900 dark:text-white">Ready to Start Earning?</h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of successful affiliates who are already earning with Etherchain. 
            Start your journey today!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/?showref=true">
              <GradientButton variant={1} className=" text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                <FontAwesomeIcon icon={faShare} className="mr-2" />
                Get Started Now
              </GradientButton>
            </a>
            <GradientButton variant={2} className="bg-gray-50 dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-900 dark:text-white font-bold py-4 px-8 rounded-xl border border-gray-200 dark:border-gray-600 transition-all duration-300 shadow-lg">
              <FontAwesomeIcon icon={faUsers} className="mr-2" />
              Learn More
            </GradientButton>
          </div>
        </div>
      </div>
      
      {/* CSS Animation for Vertical Falling Stars */}
      <style jsx>{`
        @keyframes fallStar {
          0% {
            transform: translateY(-50px) rotate(0deg);
            opacity: 0;
          }
          10% {
            opacity: 0.6;
          }
          90% {
            opacity: 0.6;
          }
          100% {
            transform: translateY(calc(100vh + 50px)) rotate(360deg);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
}
