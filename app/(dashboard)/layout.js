import React from 'react';
import { Inter } from "next/font/google";
import "../globals.css";
import { ThemeProvider } from '../components/ThemeProvider';
import { AppKit } from '@/context/AppKit';
import Navbar from './dashboard/components/Navbar';
import Footer from '../components/Footer';

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata = {
  title: "Dashboard - Etherchain AI",
  description: "Etherchain AI Dashboard",
  icons: {
    icon: '/logo_alpha.png',
  },
};

export default function DashboardLayout({ children }) {
    return (
        <html lang="en">
            <body className={`${inter.variable} bg-gray-50 dark:bg-gray-950`}>
                <ThemeProvider>
                    <AppKit>
                        <Navbar>
                            {children}
                            <Footer />
                        </Navbar>
                    </AppKit>
                </ThemeProvider>
            </body>
        </html>
    );
} 