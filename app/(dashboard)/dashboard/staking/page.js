'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

export default function StakingPage() {
  const [provider, setProvider] = useState(null);
  const [signer, setSigner] = useState(null);
  const [account, setAccount] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [activeTab, setActiveTab] = useState('stake');
  const [stakeAmount, setStakeAmount] = useState('');
  const [unstakeAmount, setUnstakeAmount] = useState('');
  const [claimAmount, setClaimAmount] = useState('0');
  const [isLoading, setIsLoading] = useState(false);

  // Mock contract addresses - replace with actual addresses
  const STAKING_CONTRACT_ADDRESS = '******************************************';
  const ETHAI_TOKEN_ADDRESS = '******************************************';

  // Mock data - in real implementation these would come from smart contracts
  const [stakingData, setStakingData] = useState({
    totalStaked: '1,234,567.89',
    totalRewards: '45,678.90',
    apy: '12.5',
    userStaked: '0',
    userRewards: '0',
    lockPeriod: '30',
    minStake: '100'
  });

  // Initialize Web3
  useEffect(() => {
    const initWeb3 = async () => {
      if (typeof window !== 'undefined' && window.ethereum) {
        try {
          const web3Provider = new ethers.BrowserProvider(window.ethereum);
          setProvider(web3Provider);
          
          // Check if already connected
          const accounts = await web3Provider.listAccounts();
          if (accounts.length > 0) {
            const signer = await web3Provider.getSigner();
            setSigner(signer);
            setAccount(accounts[0].address);
            setIsConnected(true);
            await loadStakingData();
          }
        } catch (error) {
          console.error('Error initializing Web3:', error);
        }
      }
    };

    initWeb3();
  }, []);

  // Load staking data from contract
  const loadStakingData = async () => {
    if (!provider || !account) return;

    try {
      // Mock contract calls - replace with actual contract interactions
      const stakingContract = new ethers.Contract(
        STAKING_CONTRACT_ADDRESS,
        [
          'function totalStaked() view returns (uint256)',
          'function totalRewards() view returns (uint256)',
          'function getAPY() view returns (uint256)',
          'function getUserStaked(address user) view returns (uint256)',
          'function getUserRewards(address user) view returns (uint256)',
          'function lockPeriod() view returns (uint256)',
          'function minStake() view returns (uint256)'
        ],
        provider
      );

      // In real implementation, you would call these contract methods
      // const totalStaked = await stakingContract.totalStaked();
      // const totalRewards = await stakingContract.totalRewards();
      // const apy = await stakingContract.getAPY();
      // const userStaked = await stakingContract.getUserStaked(account);
      // const userRewards = await stakingContract.getUserRewards(account);
      // const lockPeriod = await stakingContract.lockPeriod();
      // const minStake = await stakingContract.minStake();

      // For now, using mock data
    } catch (error) {
      console.error('Error loading staking data:', error);
    }
  };

  // Connect wallet
  const connectWallet = async () => {
    if (!provider) {
      alert('Please install MetaMask or another Web3 wallet');
      return;
    }

    try {
      setIsLoading(true);
      const accounts = await provider.send('eth_requestAccounts', []);
      const signer = await provider.getSigner();
      
      setSigner(signer);
      setAccount(accounts[0]);
      setIsConnected(true);
      
      await loadStakingData();
    } catch (error) {
      console.error('Error connecting wallet:', error);
      alert('Failed to connect wallet');
    } finally {
      setIsLoading(false);
    }
  };

  // Stake tokens
  const handleStake = async () => {
    if (!signer || !stakeAmount || parseFloat(stakeAmount) < parseFloat(stakingData.minStake)) {
      alert(`Minimum stake amount is ${stakingData.minStake} ETHAI`);
      return;
    }

    try {
      setIsLoading(true);
      
      const stakingContract = new ethers.Contract(
        STAKING_CONTRACT_ADDRESS,
        [
          'function stake(uint256 amount) external',
          'function approve(address spender, uint256 amount) external returns (bool)'
        ],
        signer
      );

      const ETHAIToken = new ethers.Contract(
        ETHAI_TOKEN_ADDRESS,
        [
          'function approve(address spender, uint256 amount) external returns (bool)',
          'function balanceOf(address account) view returns (uint256)'
        ],
        signer
      );

      const amount = ethers.parseEther(stakeAmount);
      
      // Check balance
      const balance = await ETHAIToken.balanceOf(account);
      if (balance < amount) {
        alert('Insufficient ETHAI balance');
        return;
      }

      // Approve tokens first
      const approveTx = await ETHAIToken.approve(STAKING_CONTRACT_ADDRESS, amount);
      await approveTx.wait();

      // Stake tokens
      const stakeTx = await stakingContract.stake(amount);
      await stakeTx.wait();

      alert('Successfully staked ETHAI tokens!');
      setStakeAmount('');
      await loadStakingData();
    } catch (error) {
      console.error('Error staking:', error);
      alert('Failed to stake tokens');
    } finally {
      setIsLoading(false);
    }
  };

  // Unstake tokens
  const handleUnstake = async () => {
    if (!signer || !unstakeAmount || parseFloat(unstakeAmount) > parseFloat(stakingData.userStaked)) {
      alert('Invalid unstake amount');
      return;
    }

    try {
      setIsLoading(true);
      
      const stakingContract = new ethers.Contract(
        STAKING_CONTRACT_ADDRESS,
        ['function unstake(uint256 amount) external'],
        signer
      );

      const amount = ethers.parseEther(unstakeAmount);
      const unstakeTx = await stakingContract.unstake(amount);
      await unstakeTx.wait();

      alert('Successfully unstaked ETHAI tokens!');
      setUnstakeAmount('');
      await loadStakingData();
    } catch (error) {
      console.error('Error unstaking:', error);
      alert('Failed to unstake tokens');
    } finally {
      setIsLoading(false);
    }
  };

  // Claim rewards
  const handleClaim = async () => {
    if (!signer || parseFloat(claimAmount) <= 0) {
      alert('No rewards to claim');
      return;
    }

    try {
      setIsLoading(true);
      
      const stakingContract = new ethers.Contract(
        STAKING_CONTRACT_ADDRESS,
        ['function claimRewards() external'],
        signer
      );

      const claimTx = await stakingContract.claimRewards();
      await claimTx.wait();

      alert('Successfully claimed rewards!');
      setClaimAmount('0');
      await loadStakingData();
    } catch (error) {
      console.error('Error claiming rewards:', error);
      alert('Failed to claim rewards');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-4 mt-8 bg-gray-50 dark:bg-gray-950 flex items-center justify-center">
      <div className="">
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-12">
              <div className="section-title text-center mb--50">
                <h2 className="title">Staking Etherchain AI</h2>
                <p className="subtitle">Stake your ETHAI tokens and earn rewards</p>
              </div>
            </div>
          </div>

          {/* Wallet Connection */}
          {!isConnected && (
            <div className="row mb--30">
              <div className="col-lg-12">
                <div className="wallet-connect">
                  <div className="text-center">
                    <h4>Connect Your Wallet</h4>
                    <p>Connect your Web3 wallet to start staking ETHAI tokens</p>
                    <button 
                      className="btn-default"
                      onClick={connectWallet}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Connecting...' : 'Connect Wallet'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Stats Cards */}
          <div className="lg:container row mb--30 grid grid-cols-2 gap-4">
            <div className="col-lg-3 col-md-6 col-sm-6">
              <div className="stats-card">
                <div className="inner">
                  <div className="icon">
                    <i className="feather-layers"></i>
                  </div>
                  <div className="content">
                    <h4 className="title">Total Staked</h4>
                    <div className="amount">{stakingData.totalStaked} ETHAI</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-6">
              <div className="stats-card">
                <div className="inner">
                  <div className="icon">
                    <i className="feather-gift"></i>
                  </div>
                  <div className="content">
                    <h4 className="title">Total Rewards</h4>
                    <div className="amount">{stakingData.totalRewards} ETHAI</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-6">
              <div className="stats-card">
                <div className="inner">
                  <div className="icon">
                    <i className="feather-trending-up"></i>
                  </div>
                  <div className="content">
                    <h4 className="title">APY</h4>
                    <div className="amount">{stakingData.apy}%</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 col-sm-6">
              <div className="stats-card">
                <div className="inner">
                  <div className="icon">
                    <i className="feather-clock"></i>
                  </div>
                  <div className="content">
                    <h4 className="title">Lock Period</h4>
                    <div className="amount">{stakingData.lockPeriod} days</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Staking Interface */}
          {isConnected && (
            <div className="row">
              <div className="col-lg-8 mb-8">
                <div className="staking-interface">
                  <div className="tabs-wrapper">
                    <div className="tabs-header">
                      <button 
                        className={`tab-button ${activeTab === 'stake' ? 'active' : ''}`}
                        onClick={() => setActiveTab('stake')}
                      >
                        <i className="feather-upload"></i>
                        Stake
                      </button>
                      <button 
                        className={`tab-button ${activeTab === 'unstake' ? 'active' : ''}`}
                        onClick={() => setActiveTab('unstake')}
                      >
                        <i className="feather-download"></i>
                        Unstake
                      </button>
                      <button 
                        className={`tab-button ${activeTab === 'claim' ? 'active' : ''}`}
                        onClick={() => setActiveTab('claim')}
                      >
                        <i className="feather-gift"></i>
                        Claim Rewards
                      </button>
                    </div>

                    <div className="tab-content">
                      {activeTab === 'stake' && (
                        <div className="stake-form">
                          <div className="form-group">
                            <label>Amount to Stake (ETHAI)</label>
                            <input
                              type="number"
                              value={stakeAmount}
                              onChange={(e) => setStakeAmount(e.target.value)}
                              placeholder={`Minimum ${stakingData.minStake} ETHAI`}
                              min={stakingData.minStake}
                              disabled={isLoading}
                            />
                          </div>
                          <div className="info-text">
                            <p>• Minimum stake: {stakingData.minStake} ETHAI</p>
                            <p>• Lock period: {stakingData.lockPeriod} days</p>
                            <p>• Current APY: {stakingData.apy}%</p>
                          </div>
                          <button 
                            className="btn-default w-100"
                            onClick={handleStake}
                            disabled={isLoading || !stakeAmount || parseFloat(stakeAmount) < parseFloat(stakingData.minStake)}
                          >
                            {isLoading ? 'Processing...' : 'Stake ETHAI'}
                          </button>
                        </div>
                      )}

                      {activeTab === 'unstake' && (
                        <div className="unstake-form">
                          <div className="form-group">
                            <label>Amount to Unstake (ETHAI)</label>
                            <input
                              type="number"
                              value={unstakeAmount}
                              onChange={(e) => setUnstakeAmount(e.target.value)}
                              placeholder="Enter amount to unstake"
                              max={stakingData.userStaked}
                              disabled={isLoading}
                            />
                          </div>
                          <div className="info-text">
                            <p>• Your staked: {stakingData.userStaked} ETHAI</p>
                            <p>• Available to unstake: {stakingData.userStaked} ETHAI</p>
                          </div>
                          <button 
                            className="btn-default w-100"
                            onClick={handleUnstake}
                            disabled={isLoading || !unstakeAmount || parseFloat(unstakeAmount) > parseFloat(stakingData.userStaked)}
                          >
                            {isLoading ? 'Processing...' : 'Unstake ETHAI'}
                          </button>
                        </div>
                      )}

                      {activeTab === 'claim' && (
                        <div className="claim-form">
                          <div className="form-group">
                            <label>Available Rewards (ETHAI)</label>
                            <input
                              type="text"
                              value={claimAmount}
                              readOnly
                              className="readonly"
                            />
                          </div>
                          <div className="info-text">
                            <p>• Total earned rewards: {stakingData.userRewards} ETHAI</p>
                            <p>• Claimable now: {claimAmount} ETHAI</p>
                          </div>
                          <button 
                            className="btn-default w-100"
                            onClick={handleClaim}
                            disabled={isLoading || parseFloat(claimAmount) <= 0}
                          >
                            {isLoading ? 'Processing...' : 'Claim Rewards'}
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="col-lg-4 mb-8">
                <div className="user-stakes">
                  <h4 className="title">Your Stakes</h4>
                  <div className="stakes-list">
                    <div className="stake-item">
                      <div className="stake-info">
                        <div className="amount">{stakingData.userStaked} ETHAI</div>
                        <div className="date">Staked on: Jan 15, 2025</div>
                      </div>
                      <div className="stake-status">
                        <span className="status-badge locked">Locked</span>
                      </div>
                    </div>
                    <div className="stake-item">
                      <div className="stake-info">
                        <div className="amount">500 ETHAI</div>
                        <div className="date">Staked on: Jan 10, 2025</div>
                      </div>
                      <div className="stake-status">
                        <span className="status-badge available">Available</span>
                      </div>
                    </div>
                  </div>

                  <div className="rewards-summary">
                    <h5>Rewards Summary</h5>
                    <div className="reward-item">
                      <span>Total Earned:</span>
                      <span>{stakingData.userRewards} ETHAI</span>
                    </div>
                    <div className="reward-item">
                      <span>Claimable:</span>
                      <span>{claimAmount} ETHAI</span>
                    </div>
                    <div className="reward-item">
                      <span>Next Reward:</span>
                      <span>In 2 days</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Info Section */}
          <div className="row">
            <div className="col-lg-12 mb-8">
              <div className="staking-info">
                <h4 className="title">Staking Information</h4>
                <div className="info-grid">
                  <div className="info-item">
                    <h5>How Staking Works</h5>
                    <p>Stake your ETHAI tokens to earn rewards. The longer you stake, the higher your rewards. Tokens are locked for a minimum period to ensure network security.</p>
                  </div>
                  <div className="info-item">
                    <h5>Rewards Calculation</h5>
                    <p>Rewards are calculated based on the amount staked, duration, and current APY. Rewards are distributed daily and can be claimed at any time.</p>
                  </div>
                  <div className="info-item">
                    <h5>Lock Period</h5>
                    <p>Tokens are locked for {stakingData.lockPeriod} days after staking. During this period, tokens cannot be unstaked but continue to earn rewards.</p>
                  </div>
                  <div className="info-item">
                    <h5>APY Rates</h5>
                    <p>Current APY is {stakingData.apy}%. Rates may vary based on total staked amount and network conditions. Higher staking amounts may earn bonus rewards.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .ETHAI-daynamic-page-content {
          min-height: 100vh;
          background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
          color: #ffffff;
        }

        .section-title {
          margin-bottom: 3rem;
        }

        .section-title .title {
          font-size: 2.5rem;
          font-weight: 700;
          margin-bottom: 1rem;
          background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .section-title .subtitle {
          font-size: 1.1rem;
          color: #a0a0a0;
        }

        .wallet-connect {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 20px;
          padding: 3rem;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          text-align: center;
        }

        .wallet-connect h4 {
          margin-bottom: 1rem;
          color: white;
        }

        .wallet-connect p {
          margin-bottom: 2rem;
          color: #a0a0a0;
        }

        .stats-card {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 15px;
          padding: 1.5rem;
          margin-bottom: 1rem;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: transform 0.3s ease;
        }

        .stats-card:hover {
          transform: translateY(-5px);
        }

        .stats-card .inner {
          display: flex;
          align-items: center;
        }

        .stats-card .icon {
          width: 50px;
          height: 50px;
          background: linear-gradient(45deg, #667eea, #764ba2);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1rem;
        }

        .stats-card .icon i {
          font-size: 1.5rem;
          color: white;
        }

        .stats-card .content .title {
          font-size: 0.9rem;
          color: #a0a0a0;
          margin-bottom: 0.5rem;
        }

        .stats-card .content .amount {
          font-size: 1.5rem;
          font-weight: 700;
          color: #ffffff;
        }

        .staking-interface {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 20px;
          padding: 2rem;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tabs-header {
          display: flex;
          margin-bottom: 2rem;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 0.5rem;
        }

        .tab-button {
          flex: 1;
          background: transparent;
          border: none;
          color: #a0a0a0;
          padding: 1rem;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
        }

        .tab-button.active {
          background: linear-gradient(45deg, #667eea, #764ba2);
          color: white;
        }

        .tab-button:hover {
          color: white;
        }

        .form-group {
          margin-bottom: 1.5rem;
        }

        .form-group label {
          display: block;
          margin-bottom: 0.5rem;
          color: #a0a0a0;
          font-weight: 500;
        }

        .form-group input {
          width: 100%;
          padding: 1rem;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.05);
          color: white;
          font-size: 1rem;
        }

        .form-group input:focus {
          outline: none;
          border-color: #667eea;
        }

        .form-group input.readonly {
          background: rgba(255, 255, 255, 0.1);
          cursor: not-allowed;
        }

        .form-group input:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .info-text {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          padding: 1rem;
          margin-bottom: 1.5rem;
        }

        .info-text p {
          margin: 0.25rem 0;
          color: #a0a0a0;
          font-size: 0.9rem;
        }

        .btn-default {
          background: linear-gradient(45deg, #667eea, #764ba2);
          border: none;
          color: white;
          padding: 1rem 2rem;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .btn-default:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-default:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .user-stakes {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 20px;
          padding: 2rem;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-stakes .title {
          margin-bottom: 1.5rem;
          color: white;
        }

        .stake-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          margin-bottom: 1rem;
        }

        .stake-info .amount {
          font-size: 1.1rem;
          font-weight: 600;
          color: white;
        }

        .stake-info .date {
          font-size: 0.8rem;
          color: #a0a0a0;
        }

        .status-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 500;
        }

        .status-badge.locked {
          background: rgba(255, 193, 7, 0.2);
          color: #ffc107;
        }

        .status-badge.available {
          background: rgba(40, 167, 69, 0.2);
          color: #28a745;
        }

        .rewards-summary {
          margin-top: 2rem;
          padding-top: 1.5rem;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .rewards-summary h5 {
          margin-bottom: 1rem;
          color: white;
        }

        .reward-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.5rem;
          color: #a0a0a0;
        }

        .staking-info {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 20px;
          padding: 2rem;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .staking-info .title {
          margin-bottom: 2rem;
          color: white;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
        }

        .info-item h5 {
          color: #667eea;
          margin-bottom: 1rem;
        }

        .info-item p {
          color: #a0a0a0;
          line-height: 1.6;
        }

        @media (max-width: 768px) {
          .tabs-header {
            flex-direction: column;
          }
          
          .stats-card .inner {
            flex-direction: column;
            text-align: center;
          }
          
          .stats-card .icon {
            margin-right: 0;
            margin-bottom: 1rem;
          }
        }
      `}</style>
    </div>
  );
}
