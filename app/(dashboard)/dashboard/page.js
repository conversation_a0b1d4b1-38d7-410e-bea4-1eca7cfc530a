'use client';

import {useState, useEffect} from 'react';
import StatsCard from '@/app/components/dashboard/StatsCard';
import RankCard from '@/app/components/dashboard/RankCard';
import RewardCard from '@/app/components/dashboard/RewardCard';
import { faWallet, faGift } from '@fortawesome/free-solid-svg-icons';
import TransactionHistory from '../../components/dashboard/TransactionHistory';
import ReferralHistory from '../../components/dashboard/ReferralHistory';
import { tokenAbi, tokenAddress, presaleAddr, presaleAbi } from '@/context/AppKit';
import { useAppKitAccount, useAppKitProvider, useAppKitNetworkCore } from '@reown/appkit/react';
import { Contract, BrowserProvider, ethers, JsonRpcProvider } from 'ethers';
import BigNumber from 'bignumber.js';

export default function Dashboard() {
  const [tokenBalance, setTokenBalance] = useState(0);
  const [referralRewards, setReferralRewards] = useState(0);
  const {isConnected, address} = useAppKitAccount();
  const {walletProvider} = useAppKitProvider("eip155");
  const {chainId} = useAppKitNetworkCore();

  useEffect(() => {
    const fetchTokenBalance = async () => {
      if(address) {
        const provider = new BrowserProvider(walletProvider);
        const tokenContract = new Contract(tokenAddress[chainId], tokenAbi, provider);
        const presaleContract = new Contract(presaleAddr[chainId], presaleAbi, provider);

        const balance = await tokenContract.balanceOf(address);
        const refBonus = await presaleContract.refBonus(address);

        setTokenBalance(balance);
        setReferralRewards(refBonus);
      }
    }

    fetchTokenBalance();
  }, [isConnected, address, chainId, walletProvider]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 pt-8">
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Monitor your ETHAI tokens and participate in the ecosystem
          </p>
        </div>

        {/* Stats Grid */}
       

        {/* Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <StatsCard title="ETHAI Balance" value={parseFloat(new BigNumber(tokenBalance).div(10 ** 18).toString()).toLocaleString("en-US")} icon={faWallet} />
              <StatsCard title="Refferal rewards" value={parseFloat(new BigNumber(referralRewards).div(10 ** 18).toString()).toLocaleString("en-US")} icon={faGift} /> 
            </div>
            <RankCard />

          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Wallet Status */}
            <RewardCard /> 
          </div>
        </div>
        <div className="grid grid-cols-1 gap-8 mt-8">
          <TransactionHistory />
          <ReferralHistory />
        </div>
      </div>
    </div>
  );
}
