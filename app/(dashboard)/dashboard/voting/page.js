'use client'

import React, { useState } from 'react';
import Link from 'next/link';

export default function VotingPage() {
    const [selectedProposal, setSelectedProposal] = useState(null);
    const [votingPower, setVotingPower] = useState(1250);
    const [isVoting, setIsVoting] = useState(false);

    const activeProposals = [
        {
            id: 1,
            title: 'Implement AI-Powered Governance Features',
            description: 'Proposal to integrate AI-driven decision making tools into the governance system, including automated proposal analysis and community sentiment tracking.',
            category: 'Governance',
            status: 'Active',
            endDate: '2024-02-15',
            votesFor: 1250,
            votesAgainst: 320,
            votesAbstain: 45,
            totalVotes: 1615,
            quorum: 2000,
            creator: 'EtherchainDA<PERSON>',
            createdDate: '2024-01-15'
        },
        {
            id: 2,
            title: 'Increase Staking Rewards by 15%',
            description: 'Proposal to boost staking rewards from 8% to 9.2% APY to incentivize more token holders to participate in network security.',
            category: 'Economics',
            status: 'Active',
            endDate: '2024-02-20',
            votesFor: 890,
            votesAgainst: 650,
            votesAbstain: 120,
            totalVotes: 1660,
            quorum: 2000,
            creator: 'StakingCommittee',
            createdDate: '2024-01-18'
        },
        {
            id: 3,
            title: 'Launch Cross-Chain Bridge to Polygon',
            description: 'Proposal to deploy a cross-chain bridge connecting Etherchain to Polygon network for improved interoperability and liquidity.',
            category: 'Infrastructure',
            status: 'Active',
            endDate: '2024-02-25',
            votesFor: 2100,
            votesAgainst: 180,
            votesAbstain: 95,
            totalVotes: 2375,
            quorum: 2000,
            creator: 'BridgeTeam',
            createdDate: '2024-01-20'
        }
    ];

    const pastProposals = [
        {
            id: 4,
            title: 'Update Tokenomics Model',
            description: 'Proposal to adjust token distribution and emission schedule for better long-term sustainability.',
            category: 'Economics',
            status: 'Executed',
            endDate: '2024-01-10',
            votesFor: 1800,
            votesAgainst: 400,
            votesAbstain: 100,
            totalVotes: 2300,
            result: 'Passed',
            executionDate: '2024-01-12'
        },
        {
            id: 5,
            title: 'Add Support for ERC-1155 Tokens',
            description: 'Proposal to extend platform support for ERC-1155 multi-token standard.',
            category: 'Development',
            status: 'Rejected',
            endDate: '2024-01-05',
            votesFor: 600,
            votesAgainst: 1200,
            votesAbstain: 150,
            totalVotes: 1950,
            result: 'Failed'
        }
    ];

    const handleVote = (proposalId, vote) => {
        setIsVoting(true);
        // Simulate voting process
        setTimeout(() => {
            setIsVoting(false);
            setSelectedProposal(null);
            // Here you would typically update the proposal votes
        }, 2000);
    };

    const getVotePercentage = (votes, total) => {
        return total > 0 ? Math.round((votes / total) * 100) : 0;
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'Active': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400';
            case 'Executed': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-400';
            case 'Rejected': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400';
            default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400';
        }
    };

    return (
        <div className="container mx-auto max-w-[100vw]">
            <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50 dark:from-gray-950 dark:via-gray-950 dark:to-purple-900 pt-16">
                {/* Hero Section */}
                <div className="relative dark:bg-gray-950">
                    <div className="absolute inset-0 bg-black/20"></div>
                    <div className="relative mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-16">
                        <div className="text-center">
                            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-white mb-4 sm:mb-6">
                                Governance Voting
                            </h1>
                            <p className="text-lg sm:text-xl text-purple-100 mb-6 sm:mb-8 max-w-3xl mx-auto px-2">
                                Shape the future of Etherchain through decentralized governance. Vote on proposals and participate in key decisions.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-2">
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 sm:p-4">
                                    <div className="text-xl sm:text-2xl font-bold text-white">{votingPower}</div>
                                    <div className="text-sm sm:text-base text-purple-100">Your Voting Power</div>
                                </div>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 sm:p-4">
                                    <div className="text-xl sm:text-2xl font-bold text-white">3</div>
                                    <div className="text-sm sm:text-base text-purple-100">Active Proposals</div>
                                </div>
                                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 sm:p-4">
                                    <div className="text-xl sm:text-2xl font-bold text-white">12</div>
                                    <div className="text-sm sm:text-base text-purple-100">Total Votes Cast</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <div className="mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
                    {/* Active Proposals */}
                    <div className="mb-8 sm:mb-12">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0 mb-6">
                            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                                Active Proposals
                            </h2>
                            <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors w-full sm:w-auto">
                                Create Proposal
                            </button>
                        </div>
                        
                        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                            {activeProposals.map((proposal) => (
                                <div key={proposal.id} className="bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                                    <div className="p-4 sm:p-6">
                                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-4">
                                            <span className={`px-3 py-1 rounded-full text-xs font-medium w-fit ${getStatusColor(proposal.status)}`}>
                                                {proposal.status}
                                            </span>
                                            <span className="text-sm text-gray-500 dark:text-gray-400">
                                                {proposal.category}
                                            </span>
                                        </div>
                                        
                                        <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                            {proposal.title}
                                        </h3>
                                        
                                        <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                                            {proposal.description}
                                        </p>
                                        
                                        {/* Voting Progress */}
                                        <div className="mb-4">
                                            <div className="flex justify-between text-sm mb-2">
                                                <span className="text-gray-600 dark:text-gray-400">Progress</span>
                                                <span className="text-gray-900 dark:text-white font-medium">
                                                    {proposal.totalVotes} / {proposal.quorum}
                                                </span>
                                            </div>
                                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                                <div 
                                                    className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                                                    style={{ width: `${Math.min((proposal.totalVotes / proposal.quorum) * 100, 100)}%` }}
                                                ></div>
                                            </div>
                                        </div>
                                        
                                        {/* Vote Distribution */}
                                        <div className="grid grid-cols-3 gap-2 mb-4">
                                            <div className="text-center">
                                                <div className="text-green-600 dark:text-green-400 font-semibold text-sm sm:text-base">
                                                    {getVotePercentage(proposal.votesFor, proposal.totalVotes)}%
                                                </div>
                                                <div className="text-xs text-gray-500 dark:text-gray-400">For</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="text-red-600 dark:text-red-400 font-semibold text-sm sm:text-base">
                                                    {getVotePercentage(proposal.votesAgainst, proposal.totalVotes)}%
                                                </div>
                                                <div className="text-xs text-gray-500 dark:text-gray-400">Against</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="text-yellow-600 dark:text-yellow-400 font-semibold text-sm sm:text-base">
                                                    {getVotePercentage(proposal.votesAbstain, proposal.totalVotes)}%
                                                </div>
                                                <div className="text-xs text-gray-500 dark:text-gray-400">Abstain</div>
                                            </div>
                                        </div>
                                        
                                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-sm text-gray-500 dark:text-gray-400 mb-4 gap-1 sm:gap-0">
                                            <span>by {proposal.creator}</span>
                                            <span>Ends {proposal.endDate}</span>
                                        </div>
                                        
                                        <button
                                            onClick={() => setSelectedProposal(proposal)}
                                            className="w-full bg-purple-600 text-white py-2 rounded-lg font-medium hover:bg-purple-700 transition-colors"
                                        >
                                            Vote Now
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Governance Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6">
                        <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-4 sm:p-6 text-white">
                            <div className="text-2xl sm:text-3xl font-bold mb-2">15</div>
                            <div className="text-sm sm:text-base text-purple-100">Total Proposals</div>
                        </div>
                        <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl p-4 sm:p-6 text-white">
                            <div className="text-2xl sm:text-3xl font-bold mb-2">8</div>
                            <div className="text-sm sm:text-base text-green-100">Executed</div>
                        </div>
                        <div className="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-xl p-4 sm:p-6 text-white">
                            <div className="text-2xl sm:text-3xl font-bold mb-2">3</div>
                            <div className="text-sm sm:text-base text-yellow-100">Active</div>
                        </div>
                        <div className="bg-gradient-to-r from-red-600 to-pink-600 rounded-xl p-4 sm:p-6 text-white">
                            <div className="text-2xl sm:text-3xl font-bold mb-2">4</div>
                            <div className="text-sm sm:text-base text-red-100">Rejected</div>
                        </div>
                    </div>
                </div>

                {/* Voting Modal */}
                {selectedProposal && (
                    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
                        <div className="bg-white dark:bg-gray-900 rounded-xl max-w-md w-full p-4 sm:p-6 mx-4">
                            <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-4">
                                Vote on Proposal
                            </h3>
                            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-6">
                                {selectedProposal.title}
                            </p>
                            
                            <div className="space-y-3 mb-6">
                                <button
                                    onClick={() => handleVote(selectedProposal.id, 'for')}
                                    disabled={isVoting}
                                    className="w-full bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50"
                                >
                                    {isVoting ? 'Voting...' : 'Vote For'}
                                </button>
                                <button
                                    onClick={() => handleVote(selectedProposal.id, 'against')}
                                    disabled={isVoting}
                                    className="w-full bg-red-600 text-white py-3 rounded-lg font-medium hover:bg-red-700 transition-colors disabled:opacity-50"
                                >
                                    {isVoting ? 'Voting...' : 'Vote Against'}
                                </button>
                                <button
                                    onClick={() => handleVote(selectedProposal.id, 'abstain')}
                                    disabled={isVoting}
                                    className="w-full bg-yellow-600 text-white py-3 rounded-lg font-medium hover:bg-yellow-700 transition-colors disabled:opacity-50"
                                >
                                    {isVoting ? 'Voting...' : 'Abstain'}
                                </button>
                            </div>
                            
                            <button
                                onClick={() => setSelectedProposal(null)}
                                disabled={isVoting}
                                className="w-full bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 rounded-lg font-medium hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors disabled:opacity-50"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
