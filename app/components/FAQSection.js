import FAQItem from './FAQItem';
import FAQAccordeon from './FAQAccordeon';

export default function FAQSection() {
  return (
    <section id="faq" className="relative py-20 bg-gray-50 dark:bg-gray-950 min-h-[75vh]">
        {/* Enhanced Background Elements */}

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-sm font-semibold text-purple-600 dark:text-purple-400 mb-2 uppercase tracking-wide">
              FAQ
            </h2>
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Frequently Asked Questions
            </h3>
          </div>

          <FAQAccordeon className="z-[2]" />
        </div>
      </section>
  )
}
