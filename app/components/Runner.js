'use client';

import React, { useEffect, useState } from 'react';

export default function Runner(props) {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const checkDarkMode = () => {
      setIsDark(document.documentElement.classList.contains('dark'));
    };
    
    checkDarkMode();
    
    // Listen for theme changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  return (
    <div 
      className={`${props.className} header-top-news mt-1`}
      style={{
        backgroundImage: isDark 
          ? "url('/images/runner-bg.png')" 
          : "url('/images/runner-light-bg.png')",
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: '50%'
      }}
    >
      <div 
        className="rfm-marquee-container marquee-text" 
        style={{
          '--pause-on-hover': 'paused',
          '--pause-on-click': 'paused',
          '--width': '100%',
          '--transform': 'none'
        }}
      >
        <div 
          className="rfm-marquee" 
          style={{
            '--play': 'running',
            '--direction': 'normal',
            '--duration': '39.67125s',
            '--delay': '0s',
            '--iteration-count': 'infinite',
            '--min-width': '100%'
          }}
        >
          <div className="rfm-initial-child-container">
            <div className="rfm-child" style={{ '--transform': 'none' }}>
              <p className="mb-0 b2 marquee-text mr--20 text-base font-medium py-1">
                We're no longer active on Telegram. Join our official Discord for real-time updates and community chat: 
                <a 
                  target="_blank" 
                  className="topbar-link dark:text-yellow-300 text-yellow-800 hover:text-yellow-100 underline mx-1" 
                  href="https://discord.gg/etherchain"
                  rel="noopener noreferrer"
                >
                  discord.gg/etherchain
                </a>
                🚧
              </p>
            </div>
            <div className="rfm-child" style={{ '--transform': 'none' }}>
              <p className="mb-0 b2 marquee-text mr--20 text-base font-medium py-1">
                We're no longer active on Telegram. Join our official Discord for real-time updates and community chat: 
                <a 
                  target="_blank" 
                  className="topbar-link dark:text-yellow-300 text-yellow-800 hover:text-yellow-100 underline mx-1" 
                  href="https://discord.gg/etherchain"
                  rel="noopener noreferrer"
                >
                  discord.gg/etherchain
                </a>
                🚧
              </p>
            </div>
            <div className="rfm-child" style={{ '--transform': 'none' }}>
              <p className="mb-0 b2 marquee-text mr--20 text-base font-medium py-1">
                We're no longer active on Telegram. Join our official Discord for real-time updates and community chat: 
                <a 
                  target="_blank" 
                  className="topbar-link dark:text-yellow-300 text-yellow-800 hover:text-yellow-100 underline mx-1" 
                  href="https://discord.gg/etherchain"
                  rel="noopener noreferrer"
                >
                  discord.gg/etherchain
                </a>
                🚧
              </p>
            </div>
            <div className="rfm-child" style={{ '--transform': 'none' }}>
              <p className="mb-0 b2 marquee-text mr--20 text-base font-medium py-1">
                We're no longer active on Telegram. Join our official Discord for real-time updates and community chat: 
                <a 
                  target="_blank" 
                  className="topbar-link dark:text-yellow-300 text-yellow-800 hover:text-yellow-100 underline mx-1" 
                  href="https://discord.gg/etherchain"
                  rel="noopener noreferrer"
                >
                  discord.gg/etherchain
                </a>
                🚧
              </p>
            </div>
            <div className="rfm-child" style={{ '--transform': 'none' }}>
              <p className="mb-0 b2 marquee-text mr--20 text-base font-medium py-1">
                We're no longer active on Telegram. Join our official Discord for real-time updates and community chat: 
                <a 
                  target="_blank" 
                  className="topbar-link dark:text-yellow-300 text-yellow-800 hover:text-yellow-100 underline mx-1" 
                  href="https://discord.gg/etherchain"
                  rel="noopener noreferrer"
                >
                  discord.gg/etherchain
                </a>
                🚧
              </p>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .header-top-news {
          overflow: hidden;
          position: relative;
        }

        .rfm-marquee-container {
          display: flex;
          overflow: hidden;
          padding: 10px 0;
          width: var(--width, 100%);
          mask-image: linear-gradient(90deg, transparent, #000 5%, #000 95%, transparent);
        }

        .rfm-marquee {
          display: flex;
          animation: rfm-marquee var(--duration, 20s) linear infinite;
          animation-play-state: var(--play, running);
          animation-delay: var(--delay, 0s);
          animation-direction: var(--direction, normal);
          animation-fill-mode: var(--fill-mode, none);
          animation-iteration-count: var(--iteration-count, infinite);
          min-width: var(--min-width, auto);
        }

        .rfm-marquee:hover {
          animation-play-state: var(--pause-on-hover, running);
        }

        .rfm-marquee:active {
          animation-play-state: var(--pause-on-click, running);
        }

        .rfm-initial-child-container {
          display: flex;
          min-width: 100%;
        }

        .rfm-child {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          white-space: nowrap;
          transform: var(--transform, none);
        }

        .marquee-text {
          color: var(--text-color, ${isDark ? '#fff' : '#000'});
          margin-right: 20px;
        }

        .topbar-link {
          transition: color 0.3s ease;
        }

        .topbar-link:hover {
          text-decoration: underline;
        }

        @keyframes rfm-marquee {
          0% {
            transform: translateX(0%);
          }
          100% {
            transform: translateX(-100%);
          }
        }



        /* Responsive adjustments */
        @media (max-width: 768px) {
          .rfm-marquee {
            animation-duration: calc(var(--duration, 20s) * 0.8);
          }
        }
      `}</style>
    </div>
  );
}
