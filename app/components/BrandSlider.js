'use client'

import React, { useEffect, useRef } from 'react';
import Image from 'next/image';

export default function BrandSlider() {
    const sliderRef = useRef(null);

    useEffect(() => {
        const slider = sliderRef.current;
        if (!slider) return;

        let animationId;
        let position = 0;
        const speed = 1; // Geschwindigkeit der Animation

        const animate = () => {
            position -= speed;
            if (position <= -slider.scrollWidth / 2) {
                position = 0;
            }
            slider.style.transform = `translateX(${position}px)`;
            animationId = requestAnimationFrame(animate);
        };

        animate();

        return () => {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        };
    }, []);

    const brands = [
        {
            id: 1,
            name: 'Partner 1',
            lightImage: '/images/download.png',
            link: '#'
        },
        {
            id: 2,
            name: 'Partner 2',
            lightImage: '/images/download1.png',
            link: '#'
        },
        {
            id: 3,
            name: 'Partner 3',
            lightImage: '/images/download2.png',
            link: '#'
        },
        {
            id: 4,
            name: 'Partner 4',
            lightImage: '/images/download3.png',
            link: '#'
        },
        {
            id: 5,
            name: 'Partner 5',
            lightImage: '/images/part2.f90b4394f928b143dff8.png',
            link: '#'
        },
        {
            id: 6,
            name: 'Partner 6',
            lightImage: '/images/part5.b6f314417a445aef57f4.png',
            link: '#'
        },
        {
            id: 7,
            name: 'Partner 7',
            lightImage: '/images/part7.2863d100af8ab056314e.png',
            link: '#'
        },
        {
            id: 8,
            name: 'Partner 8',
            lightImage: '/images/part9.320d5ae7e08adcb532f1.png',
            link: '#'
        },
        {
            id: 9,
            name: 'Partner 9',
            lightImage: '/images/part10.61d3c6e1377166100045.png',
            link: '#'
        },
        {
            id: 10,
            name: 'Partner 10',
            lightImage: '/images/part11.eb084957710bb643b399.png',
            link: '#'
        },
        {
            id: 11,
            name: 'Partner 11',
            lightImage: '/images/part12.de01bab8cdbf888b84bc.png',
            link: '#'
        },
        {
            id: 12,
            name: 'Partner 12',
            lightImage: '/images/part13.ed5a3d63ffb9d6452932.png',
            link: '#'
        },
        {
            id: 13,
            name: 'Partner 13',
            lightImage: '/images/part14.6194ab648e9cd56e810e.png',
            link: '#'
        },
        {
            id: 14,
            name: 'Partner 14',
            lightImage: '/images/part16.a195ee8a5f704076ce69.png',
            link: '#'
        },
        {
            id: 15,
            name: 'Crypto.com',
            lightImage: '/images/crypto-com-1-white.svg',
            link: '#'
        }
    ];

    return (
        <div className="w-full overflow-hidden bg-gray-50 dark:bg-gray-950 py-12">
            <div className="mx-auto px-4">
                <div className="text-center mb-8">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        Trusted by Industry Leaders
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                        Partnering with leading companies and platforms to revolutionize the blockchain ecosystem
                    </p>
                </div>
                
                <div className="relative py-24">
                    {/* Gradient Overlay Left */}
                    <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-gray-50 dark:from-gray-950 to-transparent z-10"></div>
                    
                    {/* Gradient Overlay Right */}
                    <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-gray-50 dark:from-gray-950 to-transparent z-10"></div>
                    
                    {/* Brand Slider */}
                    <div className="flex items-center space-x-20 overflow-hidden">
                        <div 
                            ref={sliderRef}
                            className="flex items-center space-x-20 transition-transform duration-1000 ease-linear"
                            style={{ minWidth: 'max-content' }}
                        >
                            {/* First set of brands */}
                            {brands.map((brand) => (
                                <div key={brand.id} className="flex-shrink-0">
                                    <a 
                                        href={brand.link} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="block group"
                                    >
                                        <div className="relative flex items-center justify-center transition-all duration-300 transform hover:scale-105">
                                            <Image
                                                src={brand.lightImage}
                                                alt={brand.name}
                                                width={150}
                                                height={60}
                                                className="max-w-full max-h-full object-contain group-hover:opacity-80 transition-opacity duration-300 bg-gray-950 p-2 rounded-lg"
                                            />
                                        </div>
                                    </a>
                                </div>
                            ))}
                            
                            {/* Duplicate set for seamless loop */}
                            {brands.map((brand) => (
                                <div key={`duplicate-${brand.id}`} className="flex-shrink-0">
                                    <a 
                                        href={brand.link} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className="block group"
                                    >
                                        <div className="relative flex items-center justify-center transition-all duration-300 transform hover:scale-105">
                                            <Image
                                                src={brand.lightImage}
                                                alt={brand.name}
                                                width={200}
                                                height={80}
                                                className="max-w-full max-h-full object-contain group-hover:opacity-80 transition-opacity duration-300 bg-gray-950"
                                            />
                                        </div>
                                    </a>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
