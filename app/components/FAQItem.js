'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';

export default function FAQItem({ index, question, answer, open, onToggle }) {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setIsOpen(open === index);
  }, [open, index]);

  const handleClick = () => {
    if (onToggle) {
      onToggle(index);
    } else {
      setIsOpen(!isOpen);
    }
  }

  return (
    <div className='flex flex-row'>
        <div className="flex items-start mr-1 sm:w-1/12 w-1/6">
            <div className='bg-white dark:bg-gray-900 rounded-lg shadow-md p-2 border border-gray-200 dark:border-gray-700'>
                <Image src="/logo_alpha.png" className="rounded-full" alt="FAQ" width={150} height={150} />
            </div>
        </div>
        <div className="bg-white dark:bg-gray-900 p-6 rounded-lg shadow-md flex flex-row sm:w-11/12 w-5/6 border border-gray-200 dark:border-gray-700">
        {isOpen ? (
        <div className="flex flex-row items-center justify-between w-full">
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">{question}</h3>
              <p className="text-gray-600 dark:text-gray-300">{answer}</p>
            </div>
            <FontAwesomeIcon icon={faChevronUp} className="text-gray-600 dark:text-gray-300 self-end cursor-pointer" size="lg" onClick={handleClick} role="button" />
        </div>
        ) : (
            <div className="flex flex-row items-center justify-between w-full">
                <h3 className="text-lg text-gray-900 dark:text-white">{question}</h3>
                <FontAwesomeIcon icon={faChevronDown} className="text-gray-600 dark:text-gray-300 self-end cursor-pointer" size="lg" onClick={handleClick} role="button" />
            </div>
        )}
      </div>
    </div>
  );
}