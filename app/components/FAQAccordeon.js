import React, { useState } from 'react';
import FAQItem from './FAQItem';
import GradientButton from './GradientButton';

export default function FAQAccordeon() {
    const [activeTab, setActiveTab] = useState('General');
    const [activeItem, setActiveItem] = useState(0);

    const handleTab = (tab, item) => {
        setActiveTab(tab);
        setActiveItem(item);
    }

    const handleToggle = (index) => {
        setActiveItem(activeItem === index ? null : index);
    }

    const tabs = [
        { id: 'General', label: 'General' },
        { id: 'Presale', label: 'Presale' },
        { id: 'Dashboard', label: 'Dashboard' },
        { id: 'Other', label: 'Other' }
    ];

    return (
        <div>
            <div className="flex flex-row justify-center space-x-1 mb-5 w-full">
                {tabs.map((tab) => (
                    <GradientButton
                        key={tab.id}
                        variant={2}
                        className={`text-xs md:text-lg sm:mr-2 mr-0 py-4 px-4 md:px-8 font-bold ${
                            activeTab === tab.id 
                                ? 'bg-purple-600 dark:bg-white text-white dark:text-purple-900' 
                                : 'text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300'
                        }`}
                        onClick={() => handleTab(tab.id, 0)}
                    >
                        {tab.label}
                    </GradientButton>
                ))}
            </div>
            <div className="flex flex-col justify-between">
                <div className={`${activeTab === 'General' ? 'block' : 'hidden'}`}>
                    <FAQItem index={0} question="What is Etherchain AI and its core mission?" answer="Etherchain AI is a cutting-edge blockchain ecosystem powered by artificial intelligence. Our mission is to revolutionize decentralized applications and governance through innovations like PoI Consensus, the Artificial Intelligence Virtual Machine (AIVM), and a Transparent AI Framework. We aim to create a smarter, more secure, and equitable blockchain ecosystem for all." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={1} question="What makes Etherchain AI's PoI Consensus unique?" answer="The Proof-of-Intelligence (PoI) Consensus leverages AI to validate transactions, ensuring both efficiency and scalability. Unlike traditional consensus models, PoI focuses on intelligent, adaptive problem-solving, reducing energy consumption while enhancing network security and throughput." open={activeItem} onToggle={handleToggle}/>
                    
                    <FAQItem index={2} question="How does the Artificial Intelligence Virtual Machine (AIVM) work?" answer="The AIVM is a transformative environment for deploying decentralized applications (dApps) powered by AI. It integrates intelligent computing capabilities directly into the blockchain, enabling developers to create advanced, data-driven applications with seamless execution and enhanced functionality." open={activeItem} onToggle={handleToggle}/>
                    
                    <FAQItem index={3} question="What are the details of the Etherchain AI presale?" answer="The Etherchain AI presale offers early adopters the opportunity to purchase tokens before the public launch. Participants benefit from discounted prices and exclusive perks, such as governance voting rights and priority access to ecosystem features like the Memecoin Launchpad." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={4} question="How does Etherchain AI ensure transparency in its ecosystem?" answer="Our Transparent AI Framework ensures every AI decision-making process is auditable and explainable. By embedding accountability into the system, we foster trust and reliability, empowering users and developers to confidently interact with Etherchain AI's technology." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={5} question="What role does decentralized governance play in Etherchain AI?" answer="Decentralized governance is at the heart of Etherchain AI. Token holders can actively participate in decision-making processes, proposing and voting on key initiatives. This ensures the ecosystem evolves in alignment with community values while leveraging AI to streamline governance operations." open={activeItem} onToggle={handleToggle}/>
                </div>
                <div className={`${activeTab === 'Presale' ? 'block' : 'hidden'}`}>
                    <FAQItem index={0} question="What is in the minimum investment?" answer="There is no minimum investment. You can invest as little as $10." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={1} question="When do I receive my ETHAI tokens?" answer="You will receive your tokens 5 days after the presale concludes, users will receive an airdrop to their wallet. Please keep up to date with the project updates via our social channels and community groups." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={2} question="Which cryptocurrencies do you accept?" answer="During the presale we accept Ethereum ETH, Tether USDT-ERC20." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={3} question="When will the presale finish?" answer="The presale will finish when all 8 stages of the presale are completed, we anticipate this would take 3 months, however to ensure a speedy launch we have implemented a $18m softcap and a $36m hardcap." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={4} question="Where can I sell my ETHAI tokens?" answer="You will be able to see your tokens once we list them on Uniswap, this will happen 5 days are the presale closes." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={5} question="Which exchanges will ETHAI tokens be listed on?" answer="We plan to list ETHAI tokens on a number of different centralized exchanges, and we will also launch ETHAi tokens on Uniswap." open={activeItem} onToggle={handleToggle}/>
                </div>
                <div className={`${activeTab === 'Dashboard' ? 'block' : 'hidden'}`}>
                    <FAQItem index={0} question="How can I see how many tokens I have purchased?" answer="You can see the tokens you have purchased by connecting your wallet to site." open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={1} question="Can I stake my ETHAI tokens?" answer="You can stake your tokens once the presale concludes. You can earn between 4% - 18% depending on the number of tokens you hold." open={activeItem} onToggle={handleToggle}/>

                </div>
                <div className={`${activeTab === 'Other' ? 'block' : 'hidden'}`}>
                    <FAQItem index={0} question="How do I contact the EtherChainAI team?" answer="You can contact the team via Live Chat or alternatively you can send an email to: <EMAIL>" open={activeItem} onToggle={handleToggle}/>

                    <FAQItem index={1} question="How can i enter the $250,000 giveaway?" answer="You can enter by completing the relevant tasks" open={activeItem} onToggle={handleToggle}/>
                </div>
            </div>
        </div>
    )
}