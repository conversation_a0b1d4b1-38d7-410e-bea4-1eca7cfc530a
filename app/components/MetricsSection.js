'use client'

import React, { useState, useEffect } from 'react';

export default function MetricsSection() {
  const [counts, setCounts] = useState({
    blocks: 2657819,
    transactions: 5663316
  });

  const metrics = [
    {
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      value: "2,657,819",
      title: "Blocks Produced",
      description: "Total blocks created"
    },
    {
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      value: "5,663,316",
      title: "On-Chain Transactions",
      description: "Total transactions processed"
    },
    {
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      value: "0.8s",
      title: "Block Time",
      description: "Average block generation time"
    },
    {
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      value: "< $0.0001",
      title: "Average Transaction Cost",
      description: "Cost per transaction"
    }
  ];

  // Load saved values from localStorage on component mount
  useEffect(() => {
    try {
      const savedCounts = localStorage.getItem('etherchain_metrics');
      const savedTimestamp = localStorage.getItem('etherchain_metrics_timestamp');
      
      if (savedCounts && savedTimestamp) {
        const parsedCounts = JSON.parse(savedCounts);
        const lastUpdate = parseInt(savedTimestamp);
        const now = Date.now();
        const timeDiff = now - lastUpdate;
        
        // Calculate how much the values should have increased since last save
        const blocksIncrease = Math.floor(timeDiff / 5000) * 15; // 15 Blöcke pro Sekunde
        const transactionsIncrease = Math.floor(timeDiff / 5000) * 45; // 45 Transaktionen pro Sekunde
        
        setCounts({
          blocks: parsedCounts.blocks + blocksIncrease,
          transactions: parsedCounts.transactions + transactionsIncrease
        });
      }
    } catch (error) {
      console.error('Error loading metrics from localStorage:', error);
    }
  }, []);

  // Animation für Zahlen
  useEffect(() => {
    let animationId;
    let startTime = Date.now();
    
    const animateNumbers = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      
      // Erhöhe die Zahlen kontinuierlich basierend auf der verstrichenen Zeit
      const blocksIncrease = Math.floor(elapsed / 5000) * 15; // 15 Blöcke pro Sekunde
      const transactionsIncrease = Math.floor(elapsed / 5000) * 45; // 45 Transaktionen pro Sekunde
      
      const newCounts = {
        blocks: counts.blocks + blocksIncrease,
        transactions: counts.transactions + transactionsIncrease
      };
      
      setCounts(newCounts);
      
      // Save to localStorage every 30 seconds
      if (elapsed % 30000 < 16) { // Every ~30 seconds
        try {
          localStorage.setItem('etherchain_metrics', JSON.stringify(newCounts));
          localStorage.setItem('etherchain_metrics_timestamp', Date.now().toString());
        } catch (error) {
          console.error('Error saving metrics to localStorage:', error);
        }
      }
      
      animationId = requestAnimationFrame(animateNumbers);
    };
    
    // Starte Animation
    animationId = requestAnimationFrame(animateNumbers);
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [counts.blocks, counts.transactions]);

  const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  return (
    <section className="relative py-20 bg-gray-50 dark:bg-gray-950 overflow-hidden">
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Network Statistics
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Real-time metrics showing the power and efficiency of the Etherchain network
          </p>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
          {metrics.map((metric, index) => (
            <div
              key={index}
              className="group flex flex-col items-center bg-white dark:bg-gray-900 rounded-2xl p-6 lg:p-8 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm hover:shadow-purple-500/10 hover:border-purple-600"
            >
              {/* Icon */}
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <div className="text-white">
                  {metric.icon}
                </div>
              </div>

              {/* Value */}
              <div className="mb-4">
                <div className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white">
                  {index === 0 ? formatNumber(counts.blocks) : 
                   index === 1 ? formatNumber(counts.transactions) : 
                   metric.value}
                </div>
              </div>

              {/* Title */}
              <h3 className="text-xl text-center font-semibold text-purple-600 dark:text-purple-400 mb-2 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
                {metric.title}
              </h3>

              {/* Description */}
              <p className="text-gray-600 dark:text-gray-300 text-sm text-center">
                {metric.description}
              </p>

              {/* Animated border effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-10 transition-opacity duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center space-x-2 bg-white dark:bg-gray-900 rounded-full px-6 py-3 shadow-lg border border-gray-200 dark:border-gray-700">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Live Network Data • Updated Every 30 Seconds
            </span>
          </div>
        </div>
      </div>
    </section>
  );
}