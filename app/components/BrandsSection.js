export default function BrandsSection() {
  return (
    <section className="relative py-16 dark:bg-gray-950 light:bg-gradient-to-bl from-gray-200 via-gray-100 to-gray-50 overflow-hidden">
      {/* Background Decorative Elements */}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-center bg-whiteitems-center space-x-8 md:space-x-12 lg:space-x-16">
          <a href="https://coinmarketcap.com/currencies/etherchain-ai/" className="flex items-center bg-white space-x-2 opacity-60 hover:opacity-100 transition-all duration-300 transform hover:scale-105 p-5 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-sm">C</span>
            </div>
            <span className="text-gray-900 font-medium">CoinMarketCap</span>
          </a>
          <a href="https://www.coingecko.com/en/coins/etherchain-ai" className="flex items-center bg-white space-x-2 opacity-60 hover:opacity-100 transition-all duration-300 transform hover:scale-105 p-5 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-sm">G</span>
            </div>
            <span className="text-gray-900 font-medium">CoinGecko</span>
          </a>
        </div>
      </div>
    </section>
  )
}
