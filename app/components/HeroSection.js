'use client'

import { useState, useEffect } from 'react';
import PresaleWidget from './PresaleWidget';
import MetricsCard from './MetricsCard';
import { faDollarSign, faUser, faHandshake, faHeadset } from '@fortawesome/free-solid-svg-icons';

export default function HeroSection() {
  // Slider effect state
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Slider effect for cycling words
  useEffect(() => {
    const words = ['Blockchain', 'Community', 'Decentralization'];
    
    const slideToNextWord = () => {
      setIsAnimating(true);
      
      // After animation completes, change the word
      setTimeout(() => {
        setCurrentWordIndex((prevIndex) => (prevIndex + 1) % words.length);
        setIsAnimating(false);
      }, 500); // Increased to 500ms for smoother transition
    };

    // Change word every 2.5 seconds (faster for better engagement)
    const interval = setInterval(slideToNextWord, 2250);

    return () => clearInterval(interval);
  }, []);

  const words = ['Blockchain', 'Community', 'Decentralization'];

  // Generate rain drops
  const generateRainDrops = () => {
    const drops = [];
    for (let i = 0; i < 5; i++) {
      drops.push(
        <div
          key={i}
          className="absolute w-0.5 h-8 bg-gradient-to-b from-pink-500/60 to-pink-300/40 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 2}s`,
            animationDuration: `${3 + Math.random()*10}s`,
            animationIterationCount: 'infinite',
            animationName: 'rainfall'
          }}
        />
      );
    }
    return drops;
  };

  return (
    <section className="relative min-h-screen light:bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 dark:bg-gray-950 overflow-hidden">
      <div className="absolute top-1/4 left-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl pointer-events-none"></div>
      <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl pointer-events-none"></div>
      {/* Rain Animation Background */}
      <div className="absolute inset-0 pointer-events-none">
        {generateRainDrops()}
      </div>

      {/* Rain Animation CSS */}
      <style jsx>{`
        @keyframes rainfall {
          0% {
            transform: translateY(-100vh);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translateY(100vh);
            opacity: 0;
          }
        }

        /* Slider Animation CSS */
        .slide .cd-words-wrapper {
          display: inline-block;
          position: relative;
          text-align: left;
        }

        .slide .cd-words-wrapper .item-text {
          display: inline-block;
          position: absolute;
          white-space: nowrap;
          left: 0;
          top: 0;
        }

        .slide .cd-words-wrapper .item-text.is-visible {
          position: relative;
        }

        .slide .cd-words-wrapper .item-text.is-hidden {
          visibility: hidden;
        }

        .slide .cd-words-wrapper .item-text.is-hidden {
          animation: slide-out 0.6s;
        }

        .slide .cd-words-wrapper .item-text.is-visible {
          animation: slide-in 0.6s;
        }

        @keyframes slide-in {
          0% {
            opacity: 0;
            transform: translateY(-100%);
          }
          60% {
            transform: translateY(20%);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slide-out {
          0% {
            opacity: 1;
            transform: translateY(0);
          }
          100% {
            opacity: 0;
            transform: translateY(100%);
          }
        }
      `}</style>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          
          <div className="lg:grid lg:grid-cols-12 gap-8 lg:gap-12 items-center">
            {/* Left Column - Hero Content */}
            <div className="lg:col-span-7 xl:col-span-8 text-center lg:text-left pt-8 lg:pt-16">
              <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 dark:text-white mb-6 lg:mb-8 leading-tight">
                <span className="block bg-gradient-to-r from-purple-600 from-20% via-purple-700 via-50% to-pink-600 to-80% text-transparent bg-clip-text">Etherchain Protocol</span>
                <span className="block">Bridging AI with</span>
                <span className="color-off animationtext slide">
                  <span className="s1 cd-words-wrapper">
                    <span className={`item-text bg-gradient-to-r from-purple-600 from-20% via-purple-700 via-50% to-pink-600 to-80% text-transparent bg-clip-text ${currentWordIndex === 0 ? 'is-visible' : 'is-hidden'}`}>Blockchain</span>
                    <span className={`item-text bg-gradient-to-r from-purple-600 from-20% via-purple-700 via-50% to-pink-600 to-80% text-transparent bg-clip-text ${currentWordIndex === 1 ? 'is-visible' : 'is-hidden'}`}>Community</span>
                    <span className={`item-text bg-gradient-to-r from-purple-600 from-20% via-purple-700 via-50% to-pink-600 to-80% text-transparent bg-clip-text ${currentWordIndex === 2 ? 'is-visible' : 'is-hidden'}`}>Decentralization</span>
                  </span>
                </span>
              </h1>

              <p className="text-lg md:text-xl lg:text-2xl text-gray-600 dark:text-gray-300 mb-8 lg:mb-12 max-w-2xl mx-auto lg:mx-0">
                Revolutionizing intelligence through decentralized innovation.
              </p>

              {/* Stats - Hidden on mobile when widget is visible */}
              <div className="hidden lg:grid grid-cols-2 xl:grid-cols-4 gap-6 lg:gap-8 max-w-3xl">
                <MetricsCard title="Raised" value="$2.5M+" icon={faDollarSign} />
                <MetricsCard title="Holders" value="10K+" icon={faUser} />
                <MetricsCard title="Partners" value="50+" icon={faHandshake} />
                <MetricsCard title="Support" value="24/7" icon={faHeadset} />
              </div>
            </div>

            {/* Right Column - Presale Widget */}
            <PresaleWidget />
          </div>

          {/* Mobile Stats - Visible only on mobile */}
          <div className="lg:hidden grid grid-cols-2 gap-6 mt-12 max-w-sm mx-auto">
            <MetricsCard title="Raised" value="$2.5M+" icon={faDollarSign} />
            <MetricsCard title="Holders" value="10K+" icon={faUser} />
            <MetricsCard title="Partners" value="50+" icon={faHandshake} />
            <MetricsCard title="Support" value="24/7" icon={faHeadset} />
          </div>
        </div>
      </section>
  )
}