'use client'
import React, { useEffect, useState } from 'react';

const GradientButton = ({ 
  children, 
  variant = 1, 
  onClick, 
  className = '', 
  disabled = false,
  type = 'button',
  ...props 
}) => {
  const [isDark, setIsDark] = useState(false);

  // Check for dark mode
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDark(document.documentElement.classList.contains('dark'));
    };
    
    checkDarkMode();
    
    // Listen for theme changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  const baseClasses = "inline-flex justify-center items-center text-center gap-2 px-4 py-2 font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl relative";
  
  const variantClasses = {
    1: "bg-gradient-to-r from-purple-600 from-20% to-pink-600 to-80% hover:from-purple-700 hover:to-pink-700 text-white",
    2: "bg-gray-100 dark:bg-gray-950 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 border-2 border-transparent bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-border"
  };

  const disabledClasses = disabled ? "opacity-50 cursor-not-allowed transform-none hover:scale-100" : "";

  // Dynamic styling for variant 2 (gradient border)
  const variant2Style = variant === 2 ? {
    background: isDark 
      ? 'linear-gradient(#0a0a0a, #0a0a0a) padding-box, linear-gradient(to right, #9333ea, #ec4899) border-box'
      : 'linear-gradient(#f3f4f6, #f3f4f6) padding-box, linear-gradient(to right, #9333ea, #ec4899) border-box',
    border: '2px solid transparent'
  } : {};

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${disabledClasses} ${className}`}
      style={variant2Style}
      {...props}
    >
      {children}
    </button>
  );
};

export default GradientButton; 