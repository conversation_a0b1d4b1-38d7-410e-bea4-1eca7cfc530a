import React from 'react';

export default function AIVMSection() {
  return (
    <section className="relative py-20 bg-white dark:bg-gray-950 overflow-hidden">
        {/* Fixed AIVM Background Elements */}
        

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1 flex justify-center">
              <div className="relative">
                <div className="w-80 h-80 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-600 rounded-full flex items-center justify-center shadow-2xl">
                  <div className="w-64 h-64 bg-white dark:bg-gray-900 rounded-full flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">AIVM</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Virtual Machine</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Artificial Intelligence Virtual Machine (AIVM)
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                The Artificial Intelligence Virtual Machine (AIVM) stands at the heart of Etherchain AI, designed as a groundbreaking computational layer to execute AI-specific tasks seamlessly on the blockchain. Unlike static systems, the AIVM is a living entity, capable of evolving through the collaborative efforts of a global developer community.
              </p>
            </div>
          </div>
        </div>
      </section>
  );
}