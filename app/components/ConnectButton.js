'use client';

import React from 'react';
import { useAppKit, useAppKitAccount } from '@reown/appkit/react';
import { shortenAddr } from '@/helpers/helpers';
import GradientButton from './GradientButton';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faWallet } from '@fortawesome/free-solid-svg-icons';

const ConnectButton = ({...props}) => {
    const { open } = useAppKit();
    const { address } = useAppKitAccount();

    const handleConnect = async () => {
        await open();
    };

    return (
        <div>
            <GradientButton 
                onClick={props.onClick || handleConnect}
                variant={props.variant || 1}
                className={`flex justify-center w-full py-2 ${props.className}`}
            >
                {typeof(address) === 'undefined' ? (<span><FontAwesomeIcon icon={faWallet} /> Connect Wallet</span>) : shortenAddr(address)}
            </GradientButton>
        </div>
    );
};

export default ConnectButton;