import React from 'react';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export default function MetricsCard({title, value, icon}) {
    return (
        <div className='flex flex-col pt-[25px] pb-[25px] pl-[55px] pr-[55px] items-center justify-center bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm hover:shadow-purple-500/10 hover:border-purple-600 transition-all duration-300 group'>
            <div className='text-purple-500 text-3xl font-bold p-[7px]'><FontAwesomeIcon icon={icon} /></div>
            <div className='text-gray-900 dark:text-white text-3xl font-bold '>{value}</div>
            <div className='text-gray-600 dark:text-white text-sm text-center pt-2'>{title}</div>
        </div>
    )
}