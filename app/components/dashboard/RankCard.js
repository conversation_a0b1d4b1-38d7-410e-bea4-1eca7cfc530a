import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMedal } from '@fortawesome/free-solid-svg-icons';

export default function RankCard() {
  return (
    <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-row gap-4 items-center">
            <div>
                <FontAwesomeIcon icon={faMedal} className="w-12 h-12 text-gray-900 dark:text-white" />
            </div>
            <div>
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Chain Scout</h2>
                <span className="text-sm text-gray-600 dark:text-gray-400">My rank</span>
            </div>
        </div>
    </div>
  );
}