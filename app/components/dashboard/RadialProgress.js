import React from 'react';

const RadialProgress = ({
  percentage,
  size = 128,
  strokeWidth = 8,
  className = ''
}) => {
  // Radius und Umfang berechnen
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  // Offset für den Fortschritt
  const strokeDashoffset = circumference * (1 - percentage / 100);

  return (
    <div
      className={`relative inline-flex ${className}`}
      style={{ width: size, height: size }}
    >
      <svg className="transform -rotate-90 w-full h-full">
        {/* Hintergrundkreis */}
        <circle
          className="text-gray-300 dark:text-gray-600"
          strokeWidth={strokeWidth}
          stroke="currentColor"
          fill="transparent"
          r={radius}
          cx={size / 2}
          cy={size / 2}
        />
        {/* Fortschrittskreis */}
        <circle
          className="text-purple-600 dark:text-purple-700"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          stroke="currentColor"
          fill="transparent"
          r={radius}
          cx={size / 2}
          cy={size / 2}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
        />
      </svg>
      {/* Prozentanzeige in der Mitte */}
      <div className="absolute inset-0 flex items-center justify-center text-xl font-semibold text-gray-900 dark:text-white">
        {percentage}%
      </div>
    </div>
  );
};

export default RadialProgress;