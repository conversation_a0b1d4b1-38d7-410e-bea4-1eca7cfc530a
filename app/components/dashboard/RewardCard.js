'use client'
import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMedal } from '@fortawesome/free-solid-svg-icons';
import RadialProgress from './RadialProgress';

export default function RewardCard() {
    const [reward, setReward] = useState(0);
    const [remaining, setRemaining] = useState(0);

    return (
        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="mb-5">
                <FontAwesomeIcon icon={faMedal} className="w-12 h-12 text-gray-900 dark:text-white" />
            </div>
            <div className="flex flex-row gap-4 items-center">
                <div>
                    <h2 className="text-3xl font-semibold text-gray-900 dark:text-white mb-4">My Rewards</h2>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{reward} Points</span>
                </div>
            </div>
            <div className="flex flex-row gap-4 items-center mb-5">
                <div>
                    <h2 className="text-3xl font-semibold text-gray-900 dark:text-white mb-4">Reward {reward} points</h2>
                    <span className="text-sm text-gray-600 dark:text-gray-400">Next Rank Reward: ~{remaining} points</span>
                </div>
            </div>
            <div className="mb-5 flex justify-center">
                <RadialProgress percentage={50} />
            </div>
        </div>
    );
}