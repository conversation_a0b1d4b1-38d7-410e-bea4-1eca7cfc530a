import React from 'react';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useAppKitAccount, useAppKitProvider, useAppKitNetworkCore } from '@reown/appkit/react';
import { Contract, BrowserProvider, ethers, JsonRpcProvider, getAddress } from 'ethers';
import { presaleAddr, presaleAbi } from '@/context/AppKit';
import BigNumber from 'bignumber.js';

export default function TransactionHistory() {
    const [transactionHistory, setTransactionHistory] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const {isConnected, address} = useAppKitAccount();
    const {walletProvider} = useAppKitProvider("eip155");
    const {chainId} = useAppKitNetworkCore();

    useEffect(() => {
        const fetchTransactionHistory = async () => {
            if(!isConnected) {
                setTransactionHistory([]);
                setError(null);
                return;
            }

            setLoading(true);
            setError(null);

            try {
                const provider = new BrowserProvider(walletProvider);
                const presaleContract = new Contract(presaleAddr[chainId], presaleAbi, provider);

                const fromBlock = await provider.getBlockNumber() - 10000;
                
                const historyETH = await presaleContract.queryFilter(presaleContract.filters.BuyTokenETH(), fromBlock, 'latest');
                const historyUSDT = await presaleContract.queryFilter(presaleContract.filters.BuyTokenUSDT(), fromBlock, 'latest');

                const history = [...historyETH, ...historyUSDT];

                const userHistory = [];
                
                // Process all items synchronously
                for (const item of history) {
                    if(item.args[0] === getAddress(address)) {
                        const timeStamp = await provider.getBlock(item.blockNumber);
                        userHistory.push({
                            amount: item.args[2],
                            txHash: item.transactionHash,
                            time: timeStamp.timestamp,
                        });
                    }
                }

                setTransactionHistory(userHistory);
            } catch (error) {
                console.error(error);
                setError("Failed to load transaction history");
                setTransactionHistory([]);
            } finally {
                setLoading(false);
            }
        }

        fetchTransactionHistory();
    }, [isConnected, address, chainId, walletProvider]);

    const formatAmount = (amount) => {
        // Convert from wei to ether and format
        const etherAmount = ethers.formatEther(amount);
        return parseFloat(etherAmount).toFixed(4);
    };

    const formatDate = (timestamp) => {
        const date = new Date(timestamp * 1000);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    };

    const formatHash = (hash) => {
        return `${hash.slice(0, 6)}...${hash.slice(-4)}`;
    };

    const openExplorer = (hash) => {
        const explorerUrl = `https://etherscan.io/tx/${hash}`;
        window.open(explorerUrl, '_blank');
    };

    return (
        <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex gap-2 items-center"><img src="/reciept.svg" className="pb-1 table-heading-icon" alt="Timestamp" />Recent Transactions</h2>
              
              {/* Summary Stats */}
              {!loading && !error && transactionHistory.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {transactionHistory.length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Total Transactions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {transactionHistory.reduce((total, item) => total + parseFloat(new BigNumber(item.amount).div(10 ** 18).toString()), 0).toLocaleString("en-US")} ETHAI
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Total Purchased</div>
                  </div>
                </div>
              )}

              <div className="space-y-4 border-t border-gray-300 dark:border-gray-700 pt-4">
                <div className="text-center overflow-x-scroll">
                    <table className="w-full text-left">
                        <thead>
                            <tr>
                                <th className="text-gray-700 dark:text-gray-300 py-2 px-4">$ Amount</th>
                                <th className="text-gray-700 dark:text-gray-300 py-2 px-4"># Transaction Hash</th>
                                <th className="flex gap-2 items-top text-gray-700 dark:text-gray-300 py-2 px-4"><img src="/timer.svg" className="pb-1 table-heading-icon" alt="Timestamp" />Timestamp</th>
                            </tr>
                        </thead>
                        <tbody>
                            {loading && (
                                <tr>
                                    <td colSpan="3" className="py-8 text-center">
                                        <svg className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7V3l-9 10z" />
                                        </svg>
                                        <p className="text-gray-600 dark:text-gray-400">Loading transaction history...</p>
                                    </td>
                                </tr>
                            )}
                            {error && (
                                <tr>
                                    <td colSpan="3" className="py-8 text-center">
                                        <svg className="w-12 h-12 text-red-400 dark:text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                        <p className="text-red-600 dark:text-red-400">{error}</p>
                                    </td>
                                </tr>
                            )}
                            {!loading && !error && transactionHistory.length === 0 && (
                                <tr>
                                    <td colSpan="3" className="py-8 text-center">
                                        <svg className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                        <p className="text-gray-600 dark:text-gray-400">No transactions yet</p>
                                        <p className="text-sm text-gray-500 dark:text-gray-500">Connect your wallet to see transaction history</p>
                                    </td>
                                </tr>
                            )}
                            {!loading && !error && transactionHistory.length > 0 && (
                                transactionHistory.map((item, index) => (
                                    <tr key={index} className="hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                                        <td className="py-2 px-4 text-green-600 dark:text-green-400 font-medium">
                                            {new BigNumber(item.amount).div(10 ** 18).toNumber().toLocaleString("en-US")} ETHAI
                                        </td>
                                        <td className="py-2 px-4">
                                            <button 
                                                onClick={() => openExplorer(item.txHash)}
                                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                                                title="View on Etherscan"
                                            >
                                                {formatHash(item.txHash)}
                                            </button>
                                        </td>
                                        <td className="py-2 px-4 text-gray-600 dark:text-gray-400">
                                            {formatDate(item.time)}
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
              </div>
            </div>
    );
}