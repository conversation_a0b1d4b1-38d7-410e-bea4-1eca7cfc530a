import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export default function StatsCard({ title, value, icon }) {
  return (
    <div className="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={icon} className="w-6 h-6 text-white" />
            </div>
        </div>
        <div className="flex items-center justify-between">
            <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{value} ETHAI</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{title}</p>
            </div>
        </div>
    </div>
  );
}