import React from 'react';
import Link from 'next/link';

export default function BlogSection() {
  const blogPosts = [
    {
      id: 1,
      title: "Etherchain AI Moves to Discord — Official Community Announcement",
      description: "Telegram has served us well during the early stages of the project, but as the ecosystem grows and more developers, validators, GPU providers, and community contributors come on board, we need a more organized, secure, and scalable space to communicate.",
      image: "https://images.ctfassets.net/86mqvksj9roh/4X8gar1c5yPHM71zn5D7le/4ea3847cf2a0bb2bc86748ee9f869d21/100.jpg",
      date: "July 4, 2025",
      slug: "etherchain-ai-moves-to-discord-official-community-announcement",
      author: "Admin"
    },
    {
      id: 2,
      title: "Etherchain AI Completes Stage 15 — Launches Bonus Round to Accelerate Mainnet and Developer Expansion",
      description: "Etherchain AI is proud to announce the successful completion of Stage 15 of its ECAI token presale. As a key milestone in our roadmap, this stage",
      image: "https://images.ctfassets.net/86mqvksj9roh/2XP0vAJIIu32WmmIEURsXA/8720e4478267dd9fede4b2e2ec588c46/1_9ADMPnLR0-6GEZNnkD3czg.webp",
      date: "July 4, 2025",
      slug: "etherchain-ai-completes-stage-15-launches-bonus-round-to-accelerate-mainnet",
      author: "Admin"
    },
    {
      id: 3,
      title: "Etherchain AI Unleashes AIVM + PoI The Dawn of Permissionless Intelligence",
      description: "In a world drowning in closed APIs, censorship layers, and gate kept model access, Etherchain AI is ripping the muzzle off...",
      image: "https://images.ctfassets.net/86mqvksj9roh/30eNxwJWhC6uy8SQ3ZPa53/51d435b9bb12151d6b92b04342e6407b/1_7IfEVa9WG4v6dSG7CnIjeQ.webp",
      date: "July 4, 2025",
      slug: "etherchain-ai-unleashes-aivm-poi-the-dawn-of-permissionless-intelligence",
      author: "Admin"
    }
  ];

  return (
    <section className="relative py-20 bg-gray-50 dark:bg-gray-950 overflow-hidden">
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">

          <h4 className="text-lg font-semibold bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600 bg-clip-text text-transparent mb-4">
            Etherchain Protocol AI
          </h4>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Transforming Blockchain & AIVM
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Transforming Tomorrow with AI-Driven Blockchain
          </p>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {blogPosts.map((post) => (
            <article key={post.id} className="group bg-white dark:bg-gray-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="relative">
                {/* Blog Image */}
                <div className="aspect-video overflow-hidden">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                
                {/* Date Badge */}
                <div className="absolute top-4 left-4">
                  <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    {post.date}
                  </span>
                </div>
              </div>

              <div className="p-6">
                {/* Author Meta */}
                <div className="flex items-center mb-4">
                  <div className="w-6 h-6 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mr-2">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                    {post.author}
                  </span>
                </div>

                {/* Blog Title */}
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300 line-clamp-2">
                  <Link href={`/blog/${post.slug}`} className="hover:underline">
                    {post.title}
                  </Link>
                </h3>

                {/* Blog Description */}
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed line-clamp-3">
                  {post.description}
                </p>

                {/* Read More Link */}
                <div className="mt-4">
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium text-sm transition-colors duration-300"
                  >
                    Read More
                    <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* View All Updates Button */}
        <div className="text-center">
          <Link 
            href="/blogs"
            className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 border border-purple-500 hover:border-purple-600"
          >
            View All Updates
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
} 