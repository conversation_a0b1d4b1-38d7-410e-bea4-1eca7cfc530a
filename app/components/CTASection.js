import Image from 'next/image';
import GradientButton from './GradientButton';

export default function CTASection() {
  return (
    <section className="relative py-20 bg-gray-50 dark:bg-gray-950 overflow-hidden">
        {/* Enhanced Background Elements */}

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-left">
              <h2 className="text-3xl md:text-5xl font-bold text-white mb-6 leading-tight">
                Secure the Future, Join Etherchain Protocol AI
              </h2>
              <p className="text-xl text-white/90 mb-8 leading-relaxed">
                Be at the forefront of technological evolution with Etherchain AI's presale. Secure exclusive access to discounted tokens and groundbreaking features like PoI Consensus and AIVM. Join a community driving intelligent, decentralized solutions and redefining blockchain innovation. Your journey into the future of AI and blockchain begins today—step into the Etherchain ecosystem now!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <GradientButton variant={1}>
                  Join Presale Now
                </GradientButton>
                <GradientButton variant={2}>
                  Learn More
                </GradientButton>
              </div>
            </div>

            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                <div className="w-80 h-80 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <div className="w-64 h-64 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center">
                    <div className="w-48 h-48 bg-white rounded-full flex items-center justify-center shadow-2xl">
                      <div className="text-center">
                        <div className="w-20 h-20 flex items-center justify-center mx-auto mb-4">
                          <Image
                            src="/logo_alpha.png"
                            alt="Etherchain Logo"
                            className="w-full h-full etherchain-logo"
                            width={50}
                            height={50}
                          />
                        </div>
                        <div className="text-purple-600 font-bold text-xl">ETHAI</div>
                        <div className="text-gray-600 text-sm">Token</div>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-white/30 rounded-full animate-bounce"></div>
                <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-white/30 rounded-full animate-bounce" style={{animationDelay: '0.5s'}}></div>
                <div className="absolute top-1/2 -left-8 w-4 h-4 bg-white/30 rounded-full animate-bounce" style={{animationDelay: '1s'}}></div>
              </div>
            </div>
          </div>
        </div>
      </section>
  )
}
