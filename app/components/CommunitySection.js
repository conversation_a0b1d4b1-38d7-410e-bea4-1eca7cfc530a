'use client'

import GradientButton from './GradientButton';

export default function CommunitySection() {
  const supportCards = [
    {
      title: "Developer Grant",
      description: "Apply for development funding",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      href: "/grant",
      buttonText: "Visit Now",
      className: ""
    },
    {
      title: "Etherchain Documentation",
      description: "Learn about our protocol",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      href: "/api-docs",
      buttonText: "See Docs",
      className: "lg:-translate-y-10"
    },
    {
      title: "Join Discord Community",
      description: "Connect with developers",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
        </svg>
      ),
      href: "https://discord.gg/etherchain",
      buttonText: "Join now",
      external: true,
      className: "lg:-translate-y-20"
    },
    {
      title: "Whitepaper",
      description: "Read our technical paper",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      href: "/whitepaper.pdf",
      buttonText: "Download PDF",
      className: "lg:-translate-y-10"
    },
    {
      title: "Block Explorer",
      description: "View blockchain data",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      href: "/explorer/txs",
      buttonText: "Visit Now",
      className: "mb-0"
    }
  ];

  return (
    <section className="relative py-20 bg-gray-50 dark:bg-gray-950 overflow-hidden">
      
      {/* Call to Action Section */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-20">
        <div className="text-center">
          <div className="bg-white dark:bg-gray-950 rounded-2xl p-8 lg:p-12 shadow-xl">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              <span className="block bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600 bg-clip-text text-transparent mb-2">
                Secure the Future
              </span>
              Get involved with the Etherchain Community
            </h2>
            
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Join thousands of developers, investors, and enthusiasts building the future of AI-powered blockchain technology.
            </p>
            
            <GradientButton 
              variant={1}
              className="px-8 py-4 text-lg"
              onClick={() => window.open('https://discord.gg/etherchain', '_blank')}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
              </svg>
              Join our Community
            </GradientButton>
          </div>
        </div>
      </div>

      {/* Support Section */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Title */}
        <div className="text-center mb-16">
          <div className="relative inline-block">
            <span className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 blur-lg opacity-20"></span>
            <h3 className="relative text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600 bg-clip-text text-transparent">
              Support
            </h3>
          </div>
        </div>

        {/* Support Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 lg:gap-8 py-20">
          {supportCards.map((card, index) => {
            // Berechne die Höhe basierend auf der Position (Berg-Effekt)
            const totalCards = supportCards.length;
            const centerIndex = Math.floor(totalCards / 2);
            const distanceFromCenter = Math.abs(index - centerIndex);
            const maxHeight = 8; // 2rem = 8 in Tailwind
            const heightOffset = distanceFromCenter * 8; // 2rem pro Schritt
            
            return (
              <div
                key={index}
                className={`group bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 flex flex-col h-auto ${card.className}`}
              >
              <div className="flex-1">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <div className="text-white">
                    {card.icon}
                  </div>
                </div>

                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                  {card.title}
                </h4>

                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                  {card.description}
                </p>

                <div className="mt-auto">
                  <a
                    href={card.href}
                    target={card.external ? "_blank" : "_self"}
                    rel={card.external ? "noopener noreferrer" : ""}
                    className="inline-flex items-center text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium transition-colors duration-300 group-hover:translate-x-1"
                  >
                    {card.buttonText}
                    <svg className="w-4 h-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          );
        })}
         </div>
      </div>
    </section>
  );
}
