export default function FeaturesSection() {
  return (
    <section className="relative py-20 bg-gray-50 dark:bg-gray-950 overflow-hidden">
      {/* Enhanced Background Elements */}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="mb-8">
            <svg className="w-16 h-16 mx-auto text-purple-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
            </svg>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Etherchain Protocol AI
          </h2>
          <h3 className="text-xl md:text-2xl font-semibold bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600 bg-clip-text text-transparent mb-6">
            Transforming Blockchain & AIVM
          </h3>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Transforming Tomorrow with AI-Driven Blockchain
          </p>
        </div>

        {/* Features Grid - Optimized for 6 items */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto">
          <div className="group bg-white dark:bg-gray-900 rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 h-full flex flex-col">
            <div className="flex-1">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>

              <h4 className="text-xl font-semibold text-purple-600 dark:text-purple-400 mb-3 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
                PoI Consensus
              </h4>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Revolutionizing consensus with Proof-of-Intelligence, a logical leap in secure and efficient decision-making.
              </p>
            </div>
          </div>

          <div className="group bg-white dark:bg-gray-900 rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 h-full flex flex-col">
            <div className="flex-1">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>

              <h4 className="text-xl font-semibold text-purple-600 dark:text-purple-400 mb-3 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
                Artificial Intelligence Virtual Machine
              </h4>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Empowering developers with futuristic, AI-driven computation for smarter decentralized applications.
              </p>
            </div>
          </div>

          <div className="group bg-white dark:bg-gray-900 rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 h-full flex flex-col">
            <div className="flex-1">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>

              <h4 className="text-xl font-semibold text-purple-600 dark:text-purple-400 mb-3 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
                Transparent AI Framework
              </h4>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Redefining trust with an open, auditable, and accountable AI system for intelligent operations.
              </p>
            </div>
          </div>

          <div className="group bg-white dark:bg-gray-900 rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 h-full flex flex-col">
            <div className="flex-1">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>

              <h4 className="text-xl font-semibold text-purple-600 dark:text-purple-400 mb-3 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
                Governance Integration
              </h4>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Seamless fusion of decentralized governance and AI for smarter, community-driven decision-making.
              </p>
            </div>
          </div>

          <div className="group bg-white dark:bg-gray-900 rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 h-full flex flex-col">
            <div className="flex-1">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>

              <h4 className="text-xl font-semibold text-purple-600 dark:text-purple-400 mb-3 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
                Memecoin Launchpad
              </h4>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Elevating the meme economy with a secure, AI-enhanced platform for creativity and monetization.
              </p>
            </div>
          </div>

          <div className="group bg-white dark:bg-gray-900 rounded-xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 dark:border-gray-700 h-full flex flex-col">
            <div className="flex-1">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>

              <h4 className="text-xl font-semibold text-purple-600 dark:text-purple-400 mb-3 group-hover:text-pink-600 dark:group-hover:text-pink-400 transition-colors duration-300">
                Decentralized Governance
              </h4>

              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Pioneering equitable, transparent decision-making powered by advanced AI and blockchain technologies.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
