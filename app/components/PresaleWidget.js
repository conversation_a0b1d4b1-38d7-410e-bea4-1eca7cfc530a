'use client'
import { useState, useEffect } from 'react';
import ProgressBar from './ProgressBar';
import ConnectButton from './ConnectButton';
import Countdown from './Countdown';
import ReferralButton from './ReferralButton';
import GradientButton from './GradientButton';
import { useAppKitBalance, useAppKitAccount, useAppKitNetworkCore, useAppKitProvider } from '@reown/appkit/react';
import { presaleAddr, presaleAbi, usdtAddress, tokenAbi, rpcUrls } from '@/context/AppKit';
import { Contract, BrowserProvider, ethers, JsonRpcProvider } from 'ethers';
import { BigNumber } from 'bignumber.js';
import { useParams } from 'next/navigation';
import { toast } from 'react-toastify';
import usePresaleData from '@/hooks/usePresaleData';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle, faQuestionCircle } from '@fortawesome/free-solid-svg-icons';

export default function PresaleWidget() {
  const params = useParams();
  const ref = params.ref;

  // Presale widget state
  const {fetchBalance} = useAppKitBalance();
  const {address, isConnected} = useAppKitAccount();
  const {chainId} = useAppKitNetworkCore();
  const {walletProvider} = useAppKitProvider("eip155");
  const [selectedToken, setSelectedToken] = useState('ETH');
  const [balance, setBalance] = useState(0);
  const [usdtBalance, setUsdtBalance] = useState(0);
  const [totalRaised, setTotalRaised] = useState(0);
  const [ethAmount, setEthAmount] = useState('');
  const [ethaiAmount, setEthaiAmount] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [tokensLeft, setTokensLeft] = useState(0);
  const [referredFrom, setReferredFrom] = useState('0x0000000000000000000000000000000000000000000000000000000000000000');
  const [isLoadingTx, setIsLoadingTx] = useState(false);
  const [allowance, setAllowance] = useState(0);

  const {presaleData, isLoading, error} = usePresaleData({
    presaleAddress: presaleAddr[chainId],
    rpcUrl: rpcUrls[chainId],
    chainId: chainId,
  });

  // Presale data
  const totalRaisedMock = 1234567;
  const targetAmount = 5000000; // $5M target

  // Calculate ETHAI amount when ETH amount changes
  useEffect(() => {
    const calculateEthaiAmount = async () => {
      const provider = new JsonRpcProvider(rpcUrls[chainId], chainId);
      const presaleContract = new Contract(presaleAddr[chainId], presaleAbi, provider);
      const usdtContract = new Contract(usdtAddress, tokenAbi, provider);

      if(address) {
        const allowance = await usdtContract.allowance(address, presaleAddr[chainId]);
        setAllowance(new BigNumber(allowance).shiftedBy(-6).toString());
      }

      if (ethAmount && !isNaN(ethAmount)) {
        switch(selectedToken) {
          case 'ETH':
            const tokenAmount = await presaleContract.getTokenAmountETH(ethers.parseEther(ethAmount));
            setEthaiAmount(ethers.formatUnits(tokenAmount, 18));
            break;
          case 'USDT':
            const tokenAmountUSD = await presaleContract.getTokenAmountUSDT(ethers.parseUnits(ethAmount, 6));
            setEthaiAmount(ethers.formatUnits(tokenAmountUSD, 18));
        }
      } else {
        setEthaiAmount('');
      }
    }

  calculateEthaiAmount();
  }, [ethAmount, selectedToken, address]);

  const handleTokenSelect = (token) => {
    setSelectedToken(token);
    setEthAmount('');
    setEthaiAmount('');
  };

  const handleEthAmountChange = (e) => {
    const value = e.target.value;
    if (value === '' || (!isNaN(value) && parseFloat(value) >= 0)) {
      setEthAmount(value);
    }
  };

  const getBalance = async () => {
    const provider = new BrowserProvider(walletProvider);
    const signer = await provider.getSigner();
    const presaleContract = new Contract(usdtAddress, tokenAbi, signer);
    const balanceUSDT = await presaleContract.balanceOf(address);
    setUsdtBalance(balanceUSDT);

    const balance = await fetchBalance();
    if(balance.data) {
      setBalance(balance.data.balance);
    }
  }


  const getReferralCode = async () => {
    const provider = new BrowserProvider(walletProvider);
    const signer = await provider.getSigner();
    const presaleContract = new Contract(presaleAddr[chainId], presaleAbi, signer);
    const referralCode = await presaleContract.addressToRefCode(address);

    setReferralCode(referralCode);

    if(ref == null) {
      setReferredFrom('0x0000000000000000000000000000000000000000000000000000000000000000');
    }
    else {
      setReferredFrom(ref);
    }
  }

  const renderPrice = () => {
    if(!presaleData) {
      return '0.0000';
    }

    if(selectedToken === 'ETH') {
      return `${parseFloat(new BigNumber(presaleData.tokenPriceETH).shiftedBy(-18).toString()).toFixed(9).toLocaleString('en-US')} ETH`;
    }
    else {
      return `${parseFloat(new BigNumber(presaleData.tokenPrice).shiftedBy(-6).toString()).toFixed(4).toLocaleString('en-US')} USDT`;
    }
  }

  const handleTokenPurchase = async () => {
    if(!address) {
      return;
    }

    try {
      setIsLoadingTx(true);
      const provider = new BrowserProvider(walletProvider);
      const signer = await provider.getSigner();
      const presaleContract = new Contract(presaleAddr[chainId], presaleAbi, signer);

      switch(selectedToken) {
        case 'ETH':
          const txETH = await presaleContract.buyTokenETH(address, referredFrom, {value: ethers.parseEther(ethAmount)});
          await txETH.wait();
          toast.success('Token purchase successful');
          break;
        case 'USDT':
          const txUSDT = await presaleContract.buyTokenUSDT(ethers.parseUnits(ethAmount, 6), address, referredFrom);
          await txUSDT.wait();
          toast.success('Token purchase successful');
          break;
      }
    } catch (error) {
      console.error("error", error);
      toast.error('Token purchase failed');
      setIsLoadingTx(false);
    } finally {
      setIsLoadingTx(false);
      setEthAmount('0');
    }
  }

  const prepareData = async () => {
    if(presaleData) {
      setTotalRaised(new BigNumber(presaleData.totalSoldUSDT).shiftedBy(-18).plus(totalRaisedMock).toString());

      setProgressPercentage(new BigNumber(presaleData.totalSoldUSDT).shiftedBy(-18).plus(totalRaisedMock).dividedBy(targetAmount).multipliedBy(100).toString());
      setTokensLeft(new BigNumber(presaleData.tokensLeft).shiftedBy(-18).toString());
    }
  }

  const handleTokenApproval = async () => {
    const provider = new BrowserProvider(walletProvider);
    const signer = await provider.getSigner();

    const usdtContract = new Contract(usdtAddress, tokenAbi, signer);
    const tx = await usdtContract.approve(presaleAddr[chainId], ethers.parseUnits(ethAmount, 6));
    await tx.wait();
    toast.success('Token approval successful');
  }

  useEffect(() => {
    getBalance();
    getReferralCode();
    prepareData();
  }, [address, chainId, selectedToken, balance, ethAmount, isLoadingTx, allowance, isConnected, presaleData]);

  const renderButtons = () => {
    if(selectedToken === 'ETH') {
      return <GradientButton onClick={() => handleTokenPurchase()} disabled={isLoadingTx}>Buy with {selectedToken}</GradientButton>
    }
    else {
      if(allowance >= parseFloat(ethAmount)) {
        return <GradientButton onClick={() => handleTokenPurchase()} disabled={isLoadingTx}>Buy with {selectedToken}</GradientButton>
      }
      else {
        return <GradientButton onClick={() => handleTokenApproval()} disabled={isLoadingTx}>Approve</GradientButton>
      }
    }
  }

  return (
    <div className="lg:col-span-5 xl:col-span-4 flex justify-center lg:justify-end mt-14 lg:mt-2">
      <div className='bg-gradient-to-r from-purple-600 from-20% via-purple-700 via-50% to-pink-600 to-80% rounded-2xl p-[1px]'>
        <div className="w-full max-w-sm lg:max-w-md xl:max-w-lg">
          {/* Enhanced Widget Container with Lightchain.ai-inspired Background */}
          <div className="relative bg-white dark:bg-gray-950 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm hover:shadow-purple-500/10 hover:border-purple-600 transition-all duration-300 group">
            {/* Background Decorative Elements - Lightchain.ai Style */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {/* Main Background Gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-900/10 via-blue-900/5 to-pink-900/10"></div>

              {/* Floating Geometric Elements with Animations */}
              <svg className="absolute -top-8 -right-8 w-32 h-32 text-purple-800 opacity-20 animate-float" fill="currentColor" viewBox="0 0 128 128">
                <circle cx="64" cy="64" r="48" opacity="0.3" />
                <path d="M64,16 Q112,32 112,64 Q96,112 64,112 Q16,96 16,64 Q32,16 64,16 Z" opacity="0.5" />
                <rect x="48" y="48" width="32" height="32" rx="8" opacity="0.4" />
              </svg>

              <svg className="absolute -bottom-6 -left-6 w-24 h-24 text-blue-800 opacity-25 animate-float-reverse" fill="currentColor" viewBox="0 0 96 96">
                <polygon points="48,8 88,28 88,68 48,88 8,68 8,28" opacity="0.6" />
                <circle cx="48" cy="48" r="16" opacity="0.4" />
              </svg>

              <svg className="absolute top-1/3 -left-4 w-16 h-16 text-pink-800 opacity-30 animate-float" stroke="currentColor" fill="none" viewBox="0 0 64 64" style={{animationDelay: '2s'}}>
                <circle cx="32" cy="32" r="24" strokeWidth="2" />
                <path d="M8,32 Q32,8 56,32 Q32,56 8,32" strokeWidth="1" opacity="0.6" />
              </svg>

              <svg className="absolute bottom-1/4 -right-2 w-20 h-20 text-purple-700 opacity-20 animate-float-reverse" fill="currentColor" viewBox="0 0 80 80" style={{animationDelay: '1s'}}>
                <path d="M40,5 L75,20 L75,60 L40,75 L5,60 L5,20 Z" />
                <circle cx="40" cy="40" r="12" fill="white" opacity="0.3" />
              </svg>

              {/* Subtle Grid Pattern */}
              <svg className="absolute inset-0 w-full h-full opacity-5" viewBox="0 0 400 600" fill="none">
                <defs>
                  <pattern id="presaleGrid" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" className="text-purple-600"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#presaleGrid)" />
              </svg>

              {/* Animated Floating Dots */}
              <div className="absolute top-8 right-12 w-2 h-2 bg-purple-400 rounded-full opacity-40 animate-pulse"></div>
              <div className="absolute top-20 right-8 w-1.5 h-1.5 bg-blue-400 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1s'}}></div>
              <div className="absolute bottom-16 left-8 w-2 h-2 bg-pink-400 rounded-full opacity-30 animate-pulse" style={{animationDelay: '2s'}}></div>
              <div className="absolute bottom-8 left-16 w-1 h-1 bg-purple-500 rounded-full opacity-60 animate-pulse" style={{animationDelay: '0.5s'}}></div>
            </div>
            {/* Enhanced Widget Header */}
            {/*<div className="relative bg-gradient-to-r from-purple-600 from-20% to-pink-600 to-80% px-6 py-4 overflow-hidden">
              
              <div className="absolute inset-0 pointer-events-none">
                <svg className="absolute -top-2 -right-2 w-16 h-16 text-white opacity-10" fill="currentColor" viewBox="0 0 64 64">
                  <circle cx="32" cy="32" r="24" />
                  <path d="M32,8 L56,24 L56,40 L32,56 L8,40 L8,24 Z" opacity="0.6" />
                </svg>

                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 animate-pulse"></div>
              </div>

              <div className="relative z-10">
                <h4 className="text-lg font-semibold text-white text-center">
                  Stage 1 - Buy ETHAI Now
                </h4>
                <p className="text-sm text-white/80 text-center mt-1">
                  Until Mainnet Launch!
                </p>
              </div>
            </div>*/}

            <div className="relative px-6 pb-4 z-10">

              {/* Divider */}
              <Countdown />
              <div className="border-t border-gray-300 dark:border-gray-700 my-6"></div>

              <div className="flex flex-col">
                <ProgressBar progressPercentage={progressPercentage} />
                <div className="flex justify-center mt-2">
                  <span className="text-sm text-gray-600 dark:text-gray-300">${parseFloat(totalRaised).toLocaleString('en-US')} / ${targetAmount.toLocaleString('en-US')} raised</span>
                </div>
              </div>

              <div className="border-t border-gray-300 dark:border-gray-700 my-6"></div>

              {/* Rank Progress Section */}
              
                
              <div className="flex flex-row justify-between items-center w-full bg-gray-200 dark:bg-gray-900 rounded-full h-7">
                {/* <div 
                  className="bg-gradient-to-r from-purple-500 to-pink-500 h-7 rounded-full transition-all duration-500 ease-out"
                  style={{ width: '5%' }}
                >
                </div> */}
                <div className="w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center z-[100]">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L15.09 8.26L22 9L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9L8.91 8.26L12 2Z"/>
                    </svg>
                  </div>
                  <span className="text-xs text-gray-600 dark:text-gray-300 font-medium">26.667 ETHAI NEEDED TO RANK UP</span>
                  <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L15.09 8.26L22 9L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9L8.91 8.26L12 2Z"/>
                    </svg>
                  </div>
              </div>
              
              {/* Token Price */}
              <div className="text-center mb-6 flex items-center gap-8">
                <hr className="border-gray-300 dark:border-gray-700 my-6 flex-grow flex-shrink" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">1 ETHAI  = {renderPrice()}</span>
                <hr className="border-gray-300 dark:border-gray-700 my-6 flex-grow flex-shrink" />
              </div>

              {/* Divider */}
              <div className="border-gray-300 dark:border-gray-700 my-6"></div>

              {/* Enhanced Token Selection */}
              <div className="flex justify-center gap-3 mb-6">
                <button
                  onClick={() => handleTokenSelect('ETH')}
                  className={`flex items-center gap-2 px-4 py-2.5 border-2 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md ${
                    selectedToken === 'ETH'
                      ? 'bg-gradient-to-r from-blue-900/30 to-blue-800/30 border-blue-500 shadow-blue-800'
                      : 'bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-600 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500'
                  }`}
                >
                  {/* ETH Token Icon with Fallback */}
                  <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 1.75L5.75 12.25L12 16L18.25 12.25L12 1.75ZM5.75 13.5L12 22.25L18.25 13.5L12 17.25L5.75 13.5Z"/>
                    </svg>
                  </div>
                  <span className="text-sm font-semibold text-gray-900 dark:text-white">ETH</span>
                </button>
                <button
                  onClick={() => handleTokenSelect('USDT')}
                  className={`flex items-center gap-2 px-4 py-2.5 border-2 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md ${
                    selectedToken === 'USDT'
                      ? 'bg-gradient-to-r from-green-900/30 to-green-800/30 border-green-500 shadow-green-800'
                      : 'bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-600 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500'
                  }`}
                >
                  {/* USDT Token Icon with Fallback */}
                  <div className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    <span>₮</span>
                  </div>
                  <span className="text-sm font-semibold text-gray-900 dark:text-white">USDT</span>
                </button>
              </div>

              {/* Divider */}
              <div className="border-t border-gray-300 dark:border-gray-700 my-6"></div>

              {/* Balance Display */}
              <div className="text-center mb-6 flex flex-row justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {selectedToken} Balance: <span className="font-semibold">{parseFloat(selectedToken === 'ETH' ? balance : (new BigNumber(usdtBalance).shiftedBy(-6).toString())).toFixed(5)}</span>
                  </p>
                </div>
                <div> 
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {parseFloat(tokensLeft).toFixed(0).toLocaleString('en-US')} ETHAI left
                  </p>
                </div>
              </div>

              {/* Divider */}
              <div className="border-t border-gray-300 dark:border-gray-700 my-6"></div>

              {/* Input Fields */}
              <div className="flex flex-row items-baseline mb-6 gap-2">
                {/* Pay with Token */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 dark:text-gray-300 mb-2">
                    Pay with {selectedToken}
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      placeholder="0"
                      value={ethAmount}
                      onChange={handleEthAmountChange}
                      className="w-full px-4 py-3 pr-12 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-900 dark:to-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-lg font-medium shadow-inner transition-all duration-200 hover:shadow-md"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      {selectedToken === 'ETH' ? (
                        <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                          <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 1.75L5.75 12.25L12 16L18.25 12.25L12 1.75ZM5.75 13.5L12 22.25L18.25 13.5L12 17.25L5.75 13.5Z"/>
                          </svg>
                        </div>
                      ) : (
                        <div className="w-6 h-6 flex items-center justify-center token-icon usdt text-sm font-bold">
                          ₮
                        </div>
                      )}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">~ ${presaleData ? parseFloat(new BigNumber(presaleData.ethPrice).shiftedBy(-6).toString()).toFixed(2).toLocaleString('en-US') : '0.00'}</p>
                </div>

                {/* Receive ETHAI */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 dark:text-gray-300 mb-2">
                    Receive ETHAI
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      readOnly
                      placeholder="0"
                      value={ethaiAmount}
                      className="w-full px-4 py-3 pr-12 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-900 dark:to-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-lg font-medium cursor-not-allowed shadow-inner"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="w-6 h-6 flex items-center justify-center etherchain-logo-container">
                        <img
                          src="/logo_alpha.png"
                          alt="Etherchain Logo"
                          className="w-full h-full object-contain etherchain-logo"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Connect Wallet Button */}
              <div className="flex flex-col items-center justify-center mb-4 w-full">
                <div className='mb-1 w-full flex flex-col justify-center space-y-1'>
                  {address ? renderButtons() : <ConnectButton className="flex items-center justify-center"/>}
                
                  <ReferralButton className="" referralCode={referralCode} />
                </div>
              </div>

              {/* Help Links */}
              <div className="flex flex-col sm:flex-row justify-center gap-4 text-sm">
                <a href="/how-to-buy" className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium text-center transition-colors">
                  <FontAwesomeIcon icon={faInfoCircle} className="h-4 w-4" /> How to Buy
                </a>
                <a href="/wallet" className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 font-medium text-center transition-colors">
                  <FontAwesomeIcon icon={faQuestionCircle} className="h-4 w-4" /> Help, My Wallet Won't Connect!
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
