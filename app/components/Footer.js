import React from 'react';
import Image from 'next/image';

const Footer = () => {
  return (
    <footer className="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-8">
            
            {/* Useful Links */}
            <div>
              <h4 className="font-semibold mb-4 text-purple-600 dark:text-purple-400">Useful Links</h4>
              <ul className="space-y-2 text-sm text-gray-900 dark:text-gray-400">
                <li><a href="/api-docs" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Documentation</a></li>
                <li><a href="/whitepaper.pdf" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Whitepaper</a></li>
                <li><a href="/announcements" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Announcements</a></li>
                <li><a href="/audits" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Audits</a></li>
                <li><a href="/api-docs" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">API References</a></li>
                <li><a href="/faq" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">FAQs</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="font-semibold mb-4 text-purple-600 dark:text-purple-400">Support</h4>
              <ul className="space-y-2 text-sm text-gray-900 dark:text-gray-400">
                <li><a href="/presale" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Presale</a></li>
                <li><a href="/how-to-buy" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">How to Buy</a></li>
                <li><a href="/support" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Support</a></li>
                <li><a href="/win-100k" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Win $100K</a></li>
                <li><a href="/grant" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Developer Grants</a></li>
              </ul>
            </div>

            {/* Legal */}
            <div>
              <h4 className="font-semibold mb-4 text-purple-600 dark:text-purple-400">Legal</h4>
              <ul className="space-y-2 text-sm text-gray-900 dark:text-gray-400">
                <li><a href="/privacy" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Privacy Policy</a></li>
                <li><a href="/risk" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Risk Disclosures</a></li>
                <li><a href="/terms" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Terms and Conditions</a></li>
              </ul>
            </div>

            {/* Blockchain */}
            <div>
              <h4 className="font-semibold mb-4 text-purple-600 dark:text-purple-400">Blockchain</h4>
              <ul className="space-y-2 text-sm text-gray-900 dark:text-gray-400">
                <li><a href="/explorer/txs" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Explorer (Testnet)</a></li>
                <li><a href="/explorer/contracts/verifier" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Contract Verifier</a></li>
                <li><a href="/faucet" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Faucet</a></li>
                <li><a href="/bridge" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Bridge</a></li>
                <li><a href="/api-docs" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">API Docs</a></li>
              </ul>
            </div>

            {/* Community */}
            <div>
              <h4 className="font-semibold mb-4 text-purple-600 dark:text-purple-400">Community</h4>
              <ul className="space-y-2 text-sm text-gray-900 dark:text-gray-400">
                <li><a href="/forum" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Forum</a></li>
                <li><a href="/support" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Developer Support</a></li>
                <li><a href="/dashboard/voting" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Governance Voting</a></li>
                <li><a href="/bug-reports" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Bug Reports</a></li>
              </ul>
            </div>

            {/* Socials */}
            <div>
              <h4 className="font-semibold mb-4 text-purple-600 dark:text-purple-400">Socials</h4>
              <ul className="space-y-2 text-sm text-gray-900 dark:text-gray-400">
                <li><a href="https://x.com/EtherchainAI" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Twitter (X)</a></li>
                <li><a href="https://discord.gg/etherchain" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Discord</a></li>
                <li><a href="https://linktr.ee/etherchain" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Linktree</a></li>
                <li><a href="https://coinmarketcap.com/currencies/etherchain" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">CoinMarketCap</a></li>
                <li><a href="https://etherchain.community/" className="footer-link hover:text-purple-600 dark:hover:text-purple-400 transition-colors">Community Site</a></li>
              </ul>
            </div>
          </div>

          
          <div className="flex flex-row items-center justify-between border-t border-gray-300 dark:border-gray-800 mt-8 pt-8 text-center text-sm text-gray-900 dark:text-gray-400">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-12 h-12 flex items-center justify-center">
                <Image
                  src="/logo_alpha.png"
                  alt="Etherchain Logo"
                  className="w-full h-full etherchain-logo"
                  width={50}
                  height={50}
                />
              </div>
              <span className="text-2xl font-bold text-gray-900 dark:text-white">ETHERCHAIN</span>
            </div>
            <div>
              <p>&copy; 2025 <a href="https://etherchain.ai" className="text-gray-950 dark:text-white hover:text-purple-600 dark:hover:text-purple-400">Etherchain Protocol</a>. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    );
};

export default Footer;