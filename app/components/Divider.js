import React, { useState, useEffect } from 'react';

export default function Divider() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const checkDarkMode = () => {
    setIsDarkMode(document.documentElement.classList.contains('dark'));
    };
    
    checkDarkMode();
    
    // Listen for theme changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  return (
    <div className="w-full">
      <img className='w-full' src={isDarkMode ? '/images/divider-dark.svg' : '/images/divider-light.svg'} alt="divider"/>
    </div>
  );
}