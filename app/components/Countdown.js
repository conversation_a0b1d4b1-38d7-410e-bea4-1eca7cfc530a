'use client'

import { useState, useEffect } from 'react';
import { useAppKitNetworkCore } from '@reown/appkit/react';
import { rpcUrls, presaleAddr, presaleAbi } from '@/context/AppKit';
import { JsonRpcProvider, Contract, ethers } from 'ethers';
import { BigNumber } from 'bignumber.js';

export default function Countdown() {
  const {chainId} = useAppKitNetworkCore();
  const [startTimestamp, setStartTimestamp] = useState(0);
  
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  const getStartTimestamp = async () => {
    const provider = new JsonRpcProvider(rpcUrls[chainId], chainId);
    const presaleContract = new Contract(presaleAddr[chainId], presaleAbi, provider);
   
    const startTimestamp = await presaleContract.presaleStartTime();
    setStartTimestamp(startTimestamp);
  }

  useEffect(() => {
    getStartTimestamp();
  }, []);

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date();
      
      if (!startTimestamp || startTimestamp === 0) {
        return;
      }

      // Konvertiere den Start-Timestamp zu einem Date-Objekt
      const startDate = new Date(BigNumber(startTimestamp).toNumber() * 1000);
      
      // Berechne die nächste 24-Stunden-Periode basierend auf dem Start-Timestamp
      const timeSinceStart = now.getTime() - startDate.getTime();
      const hoursSinceStart = timeSinceStart / (1000 * 60 * 60);
      const completed24HourCycles = Math.floor(hoursSinceStart / 24);
      const nextCycleStart = new Date(startDate.getTime() + (completed24HourCycles + 1) * 24 * 60 * 60 * 1000);
      
      const difference = nextCycleStart - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
        const minutes = Math.floor((difference / 1000 / 60) % 60);
        const seconds = Math.floor((difference / 1000) % 60);
        
        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        // Fallback: Falls etwas schief geht, setze auf 0
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    };

    // Calculate initial time
    calculateTimeLeft();

    // Update every second
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [startTimestamp, chainId]);

  return (
    <>
      <div className="ethai-countdown-container">
        <div className="ethai-countdown-box">
          <div className="ethai-countdown-time">{timeLeft.days}</div>
          <div className="ethai-countdown-label">Days</div>
        </div>
        <div className="ethai-countdown-box">
          <div className="ethai-countdown-time">{timeLeft.hours}</div>
          <div className="ethai-countdown-label">Hours</div>
        </div>
        <div className="ethai-countdown-box">
          <div className="ethai-countdown-time">{timeLeft.minutes}</div>
          <div className="ethai-countdown-label">Minutes</div>
        </div>
        <div className="ethai-countdown-box">
          <div className="ethai-countdown-time">{timeLeft.seconds}</div>
          <div className="ethai-countdown-label">Seconds</div>
        </div>
      </div>

      <style jsx>{`
        .ethai-countdown-container {
          display: flex;
          gap: 5px;
          justify-content: center;
          align-items: center;
          margin-top: 20px;
        }

        .ethai-countdown-box {
          padding: 5px 8px;
          border-radius: 12px;
          text-align: center;
          color: #0f1021;
          min-width: 60px;
          position: relative;
        }

        .ethai-countdown-box:after, .ethai-countdown-box:before {
          content: "";
          position: absolute;
          left: 0;
          width: 100%;
          height: 50%;
          background: #7376aa;
          z-index: -1;
        }

        .ethai-countdown-box:before {
          top: 0;
          border-radius: 10px 10px 3px 3px;
          border: 1px solid #7375a6;
          box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, .27);
          border-bottom: 0;
        }

        .ethai-countdown-box:after {
          top: 50%;
          border-radius: 3px 3px 10px 10px;
          border: 1px solid #7375a6;
          box-shadow: inset 0 -2px 2px 0 rgba(0, 0, 0, .18);
          border-top: 0;
        }

        .ethai-countdown-time {
          font-family: Bebas Neue, sans-serif;
          font-size: 34px;
          font-weight: 700;
        }

        .ethai-countdown-label {
          font-size: 10px;
          opacity: .8;
          margin-top: 4px;
          position: absolute;
          bottom: 5px;
          text-align: center;
          width: 100%;
          left: 0;
        }
      `}</style>
    </>
  );
}
