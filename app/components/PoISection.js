import React from 'react';

export default function PoISection() {
  return (
    <section className="relative py-20 light:bg-gray-100 dark:bg-gray-950 overflow-hidden">
      {/* Fixed PoI Background Elements */}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Proof of Intelligence (PoI)
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
              Etherchain AI introduces Proof of Intelligence (PoI), a revolutionary consensus mechanism designed to reward nodes for performing valuable AI computations, such as model training, inference, and optimization tasks. Unlike traditional consensus mechanisms such as Proof of Work (PoW) or Proof of Stake (PoS), PoI incentivizes meaningful contributions to AI development while maintaining network security.
            </p>
          </div>
          <div className="flex justify-center">
            <div className="relative">
              <div className="w-80 h-80 bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600 rounded-full flex items-center justify-center shadow-2xl">
                <div className="w-64 h-64 bg-white dark:bg-gray-900 rounded-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">PoI</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Consensus</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}