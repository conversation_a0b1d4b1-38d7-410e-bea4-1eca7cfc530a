export default function TokenomicsSection() {
  return (
    <section className="relative py-20 light:bg-gray-100 dark:bg-gray-950 overflow-hidden w-full">
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-sm font-semibold text-purple-600 dark:text-purple-400 mb-2 uppercase tracking-wide">
              Presale Token Allocation
            </h2>
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Tokenomics
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Etherchain AI Tokenomics
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="flex justify-center">
              <div className="relative w-80 h-80">
                <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                  <circle cx="50" cy="50" r="40" fill="none" stroke="gray-300 dark:stroke-gray-600" strokeWidth="8"/>
                  {/* Presale: 50% */}
                  <circle cx="50" cy="50" r="40" fill="none" stroke="url(#tokenomicsGradient)" strokeWidth="8" strokeDasharray="50 50" strokeLinecap="round"/>
                  {/* Liquidity: 25% */}
                  <circle cx="50" cy="50" r="40" fill="none" stroke="url(#tokenomicsGradient2)" strokeWidth="8" strokeDasharray="25 75" strokeDashoffset="-50" strokeLinecap="round"/>
                  {/* Marketing: 10% */}
                  <circle cx="50" cy="50" r="40" fill="none" stroke="url(#tokenomicsGradient3)" strokeWidth="8" strokeDasharray="10 90" strokeDashoffset="-75" strokeLinecap="round"/>
                  {/* Investors: 8% */}
                  <circle cx="50" cy="50" r="40" fill="none" stroke="url(#tokenomicsGradient4)" strokeWidth="8" strokeDasharray="8 92" strokeDashoffset="-85" strokeLinecap="round"/>
                  {/* Giveaway & Airdrops: 5% */}
                  <circle cx="50" cy="50" r="40" fill="none" stroke="url(#tokenomicsGradient5)" strokeWidth="8" strokeDasharray="5 95" strokeDashoffset="-93" strokeLinecap="round"/>
                  {/* Buyback & Burn: 2% */}
                  <circle cx="50" cy="50" r="40" fill="none" stroke="url(#tokenomicsGradient6)" strokeWidth="8" strokeDasharray="2 98" strokeDashoffset="-98" strokeLinecap="round"/>
                  <defs>
                    <linearGradient id="tokenomicsGradient">
                      <stop offset="0%" stopColor="#512da8" />
                      <stop offset="100%" stopColor="#673ab7" />
                    </linearGradient>
                    <linearGradient id="tokenomicsGradient2">
                      <stop offset="0%" stopColor="#673ab7" />
                      <stop offset="100%" stopColor="#e91e63" />
                    </linearGradient>
                    <linearGradient id="tokenomicsGradient3">
                      <stop offset="0%" stopColor="#e91e63" />
                      <stop offset="100%" stopColor="#9333ea" />
                    </linearGradient>
                    <linearGradient id="tokenomicsGradient4">
                      <stop offset="0%" stopColor="#9333ea" />
                      <stop offset="100%" stopColor="#3b82f6" />
                    </linearGradient>
                    <linearGradient id="tokenomicsGradient5">
                      <stop offset="0%" stopColor="#3b82f6" />
                      <stop offset="100%" stopColor="#10b981" />
                    </linearGradient>
                    <linearGradient id="tokenomicsGradient6">
                      <stop offset="0%" stopColor="#10b981" />
                      <stop offset="100%" stopColor="#f59e0b" />
                    </linearGradient>
                  </defs>
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">ETHAI</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Token</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-gradient-to-r from-purple-600 to-purple-700 rounded-full"></div>
                  <span className="text-gray-900 dark:text-white font-medium">Presale</span>
                </div>
                <div className="text-right">
                  <span className="text-gray-600 dark:text-gray-300 font-semibold">50%</span>
                  <div className="text-xs text-gray-500 dark:text-gray-400">25,000,000 tokens</div>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-gradient-to-r from-purple-700 to-pink-600 rounded-full"></div>
                  <span className="text-gray-900 dark:text-white font-medium">Liquidity</span>
                </div>
                <div className="text-right">
                  <span className="text-gray-600 dark:text-gray-300 font-semibold">25%</span>
                  <div className="text-xs text-gray-500 dark:text-gray-400">12,500,000 tokens</div>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-gradient-to-r from-pink-600 to-purple-600 rounded-full"></div>
                  <span className="text-gray-900 dark:text-white font-medium">Marketing</span>
                </div>
                <div className="text-right">
                  <span className="text-gray-600 dark:text-gray-300 font-semibold">10%</span>
                  <div className="text-xs text-gray-500 dark:text-gray-400">5,000,000 tokens</div>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full"></div>
                  <span className="text-gray-900 dark:text-white font-medium">Investors</span>
                </div>
                <div className="text-right">
                  <span className="text-gray-600 dark:text-gray-300 font-semibold">8%</span>
                  <div className="text-xs text-gray-500 dark:text-gray-400">4,000,000 tokens</div>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-gradient-to-r from-blue-600 to-green-500 rounded-full"></div>
                  <span className="text-gray-900 dark:text-white font-medium">Giveaway & Airdrops</span>
                </div>
                <div className="text-right">
                  <span className="text-gray-600 dark:text-gray-300 font-semibold">5%</span>
                  <div className="text-xs text-gray-500 dark:text-gray-400">2,500,000 tokens</div>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full"></div>
                  <span className="text-gray-900 dark:text-white font-medium">Buyback & Burn</span>
                </div>
                <div className="text-right">
                  <span className="text-gray-600 dark:text-gray-300 font-semibold">2%</span>
                  <div className="text-xs text-gray-500 dark:text-gray-400">1,000,000 tokens</div>
                </div>
              </div>

              <div className="mt-8 p-6 bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                <div className="mb-4">
                  <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Total Supply:</div>
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">50,000,000 ETHAI</div>
                </div>

                <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Contract Address:</div>
                <div className="flex sm:flex-row flex-col items-center space-x-2">
                  <code className="text-xs bg-gray-100 dark:bg-gray-600 px-3 py-2 rounded font-mono text-gray-700 dark:text-gray-200 flex-1 overflow-clip">
                    ******************************************
                  </code>
                </div>
                <div className="flex flex-row justify-between">
                  <a href="#" className="inline-flex items-center mt-3 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 text-sm font-medium">
                    View Contract
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                  <a role="button" className="inline-flex items-center mt-3 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 text-sm font-medium">
                    click to copy
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
  )
}
