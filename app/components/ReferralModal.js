'use client'
import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

export default function ReferralModal({ isOpen, onClose, referralCode = 'GvUWmC' }) {
  const [copied, setCopied] = useState(false);
  const [mounted, setMounted] = useState(false);
  const referralLink = `${process.env.NEXT_PUBLIC_APP_URL}${referralCode}`;

  // Ensure component is mounted before rendering portal
  useEffect(() => {
    setMounted(true);
  }, []);

  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Close modal when clicking outside
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(referralLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  if (!isOpen || !mounted) return null;

  // Create modal content
  const modalContent = (
    <div 
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 99999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '1rem',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(4px)'
      }}
      onClick={handleBackdropClick}
      data-referral-modal="true"
      data-testid="referral-modal-overlay"
    >
      <div style={{ position: 'relative', width: '100%', maxWidth: '28rem' }}>
        {/* Enhanced Modal Content with Etherchain Design */}
        <div className="relative bg-white dark:bg-gray-950 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm">
          {/* Background Decorative Elements */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {/* Main Background Gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-900/10 via-blue-900/5 to-pink-900/10"></div>

            {/* Floating Geometric Elements */}
            <svg className="absolute -top-6 -right-6 w-24 h-24 text-purple-800 opacity-20 animate-float" fill="currentColor" viewBox="0 0 96 96">
              <circle cx="48" cy="48" r="36" opacity="0.3" />
              <path d="M48,12 Q84,24 84,48 Q72,84 48,84 Q12,72 12,48 Q24,12 48,12 Z" opacity="0.5" />
            </svg>

            <svg className="absolute -bottom-4 -left-4 w-20 h-20 text-blue-800 opacity-25 animate-float-reverse" fill="currentColor" viewBox="0 0 80 80">
              <polygon points="40,6 74,20 74,60 40,74 6,60 6,20" opacity="0.6" />
              <circle cx="40" cy="40" r="12" opacity="0.4" />
            </svg>

            {/* Animated Floating Dots */}
            <div className="absolute top-6 right-8 w-2 h-2 bg-purple-400 rounded-full opacity-40 animate-pulse"></div>
            <div className="absolute bottom-6 left-8 w-1.5 h-1.5 bg-blue-400 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1s'}}></div>
          </div>

          {/* Modal Header */}
          <div className="relative bg-gradient-to-r from-purple-600 via-purple-700 to-pink-600 px-6 py-4 overflow-hidden">
            {/* Header Background Pattern */}
            <div className="absolute inset-0 pointer-events-none">
              <svg className="absolute -top-2 -right-2 w-12 h-12 text-white opacity-10" fill="currentColor" viewBox="0 0 48 48">
                <circle cx="24" cy="24" r="18" />
                <path d="M24,6 L42,18 L42,30 L24,42 L6,30 L6,18 Z" opacity="0.6" />
              </svg>
              {/* Subtle shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 animate-pulse"></div>
            </div>

            <div className="relative z-10">
              <h5 className="text-lg font-semibold text-white text-center">
                Your referral link
              </h5>
            </div>
          </div>

          {/* Modal Body */}
          <div className="relative p-6 z-10">
            <p className="text-sm text-gray-600 dark:text-gray-300 text-center mb-6">
              Share your link to your friends to receive some bonus!
            </p>

            {/* Referral Link Input */}
            <div className="mb-6">
              <div className="relative flex items-center border-2 border-gradient-to-r from-purple-500 from-20% to-pink-500 to-80% dark:from-gray-700 dark:to-gray-600 rounded-lg overflow-hidden shadow-inner">
                <input
                  type="text"
                  readOnly
                  value={referralLink}
                  className="flex-1 px-4 py-3 bg-transparent text-gray-900 dark:text-white text-sm font-medium focus:outline-none"
                />
                <button
                  onClick={copyToClipboard}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold text-sm transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  {copied ? (
                    <div className="flex items-center gap-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      Copied!
                    </div>
                  ) : (
                    'Copy'
                  )}
                </button>
              </div>
            </div>

            {/* Bonus Info */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L15.09 8.26L22 9L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9L8.91 8.26L12 2Z"/>
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-900 dark:text-white">
                    Earn Rewards
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-300">
                    Get bonus ETHAI tokens for each successful referral
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 w-10 h-10 bg-white/10 hover:bg-white/20 dark:bg-gray-900/50 dark:hover:bg-gray-900/70 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110 z-20 cursor-pointer referral-modal-close-button"
            style={{ 
              zIndex: 100001,
              pointerEvents: 'auto'
            }}
            type="button"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );

  // Render using portal to ensure it's at the end of DOM tree
  return createPortal(modalContent, document.body);
} 