'use client'
import { useState } from 'react';
import ReferralModal from './ReferralModal';
import GradientButton from './GradientButton';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShareFromSquare } from '@fortawesome/free-solid-svg-icons';

export default function ReferralButton({ referralCode = 'GvUWmC' }) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <GradientButton
        onClick={() => setIsModalOpen(true)}
        variant={2}
        data-testid="referral-button"
      >
        <span><FontAwesomeIcon icon={faShareFromSquare} /> Share Referral Link</span>
      </GradientButton>

      <ReferralModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        referralCode={referralCode}
      />
    </>
  );
} 