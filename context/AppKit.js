'use client';

import { createAppKit } from '@reown/appkit/react';
import { EthersAdapter } from '@reown/appkit-adapter-ethers';
import { mainnet, defineChain } from '@reown/appkit/networks';

import presaleABI from "./presaleABI.json";
import tokenABI from "./TokenABI.json";
import faucetABI from "./faucetABI.json";

const projectId = '0c82d60004fc76bbed53e2403cb22ef5';

const metadata = {
    name: 'Etherchain',
    description: 'Etherchain is a new ai driven blockchain based on the ethereum network.',
    icon: ['https://etherchain.ai/logo_alpha_2.png'],
    url: 'https://etherchain.ai',    
};

export const usdtAddress = "******************************************";

export const presaleAddr = {
    1: "",
    1337: "******************************************"
};

export const faucetAddress = {
    1: "",
    1337: "******************************************"
}

export const tokenAddress = {
    1: "",
    1337: "******************************************"
}

export const rpcUrls = {
    1: "https://eth.llamarpc.com",
    1337: "https://ethnode.pxlfussel.org/"
}

export const presaleAbi = presaleABI;
export const tokenAbi = tokenABI;

const pxlTestnet = defineChain({
    id: 1337,
    name: 'PXL Testnet',
    chainNamespace: 'eip155',
    caipNetworkId: 'eip155:1337',
    nativeCurrency: {
        decimals: 18,
        name: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['https://ethnode.pxlfussel.org/']
        }
    }
})


export const modal = createAppKit({
    adapters: [new EthersAdapter()],
    metadata,
    networks: [mainnet, pxlTestnet],
    projectId,
    features: {
        analytics: true,
        onramp: false,
        swaps: false,
        smartSessions: false,
        email: false,
        socials: false,
    },
});

export function AppKit({ children }) {
    return (
        <>
            {children}
        </>
    );
}