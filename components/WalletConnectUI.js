'use client';

import React, { useEffect, useState } from 'react';
import { useAppKit, useAppKitAccount, useDisconnect } from '@reown/appkit/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faWallet, faCopy, faSignOutAlt, faCheckCircle } from '@fortawesome/free-solid-svg-icons';

/**
 * Utility function to shorten wallet addresses
 * @param {string} addr - The wallet address to shorten
 * @returns {string} - Shortened address in format "0x123...abc"
 */
export const shortenAddr = (addr) => {
    if (!addr) return '';
    return addr.slice(0, 5) + "..." + addr.slice(-5);
};

/**
 * Gradient Button Component
 * Reusable button with gradient styling and dark mode support
 */
const GradientButton = ({ 
  children, 
  variant = 1, 
  onClick, 
  className = '', 
  disabled = false,
  type = 'button',
  ...props 
}) => {
  const [isDark, setIsDark] = useState(false);

  // Check for dark mode
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDark(document.documentElement.classList.contains('dark'));
    };
    
    checkDarkMode();
    
    // Listen for theme changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  const baseClasses = "inline-flex justify-center items-center text-center gap-2 px-4 py-2 font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl relative";
  
  const variantClasses = {
    1: "bg-gradient-to-r from-purple-600 from-20% to-pink-600 to-80% hover:from-purple-700 hover:to-pink-700 text-white",
    2: "bg-gray-100 dark:bg-gray-950 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 border-2 border-transparent bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-border"
  };

  const disabledClasses = disabled ? "opacity-50 cursor-not-allowed transform-none hover:scale-100" : "";

  // Dynamic styling for variant 2 (gradient border)
  const variant2Style = variant === 2 ? {
    background: isDark 
      ? 'linear-gradient(#0a0a0a, #0a0a0a) padding-box, linear-gradient(to right, #9333ea, #ec4899) border-box'
      : 'linear-gradient(#f3f4f6, #f3f4f6) padding-box, linear-gradient(to right, #9333ea, #ec4899) border-box',
    border: '2px solid transparent'
  } : {};

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${disabledClasses} ${className}`}
      style={variant2Style}
      {...props}
    >
      {children}
    </button>
  );
};

/**
 * Basic Connect Button Component
 * Simple wallet connect button that shows address when connected
 */
export const ConnectButton = ({...props}) => {
    const { open } = useAppKit();
    const { address } = useAppKitAccount();

    const handleConnect = async () => {
        await open();
    };

    return (
        <div>
            <GradientButton 
                onClick={props.onClick || handleConnect}
                variant={props.variant || 1}
                className={`flex justify-center w-full py-2 ${props.className}`}
            >
                {typeof(address) === 'undefined' ? (
                    <span><FontAwesomeIcon icon={faWallet} /> Connect Wallet</span>
                ) : (
                    shortenAddr(address)
                )}
            </GradientButton>
        </div>
    );
};

/**
 * Advanced Wallet Widget Component
 * Full-featured wallet widget with connect/disconnect, address display, and copy functionality
 */
export const WalletWidget = ({ 
    className = '',
    showBalance = false,
    showNetwork = false,
    variant = 1 
}) => {
    const { open } = useAppKit();
    const { address, isConnected } = useAppKitAccount();
    const { disconnect } = useDisconnect();
    const [copied, setCopied] = useState(false);

    const handleConnect = async () => {
        await open();
    };

    const handleDisconnect = async () => {
        await disconnect();
    };

    const copyAddress = async () => {
        if (address) {
            await navigator.clipboard.writeText(address);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        }
    };

    if (!isConnected) {
        return (
            <div className={`wallet-widget ${className}`}>
                <GradientButton 
                    onClick={handleConnect}
                    variant={variant}
                    className="flex justify-center w-full py-3"
                >
                    <FontAwesomeIcon icon={faWallet} className="mr-2" />
                    Connect Wallet
                </GradientButton>
            </div>
        );
    }

    return (
        <div className={`wallet-widget bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">Connected</span>
                </div>
                <button
                    onClick={handleDisconnect}
                    className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 transition-colors"
                    title="Disconnect"
                >
                    <FontAwesomeIcon icon={faSignOutAlt} />
                </button>
            </div>
            
            <div className="space-y-2">
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Address:</span>
                    <div className="flex items-center space-x-2">
                        <span className="text-sm font-mono text-gray-900 dark:text-white">
                            {shortenAddr(address)}
                        </span>
                        <button
                            onClick={copyAddress}
                            className="text-gray-500 hover:text-purple-500 dark:text-gray-400 dark:hover:text-purple-400 transition-colors"
                            title="Copy address"
                        >
                            <FontAwesomeIcon icon={copied ? faCheckCircle : faCopy} />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

/**
 * Wallet Status Indicator Component
 * Simple status indicator showing connection state
 */
export const WalletStatus = ({ className = '' }) => {
    const { isConnected, address } = useAppKitAccount();

    return (
        <div className={`wallet-status flex items-center space-x-2 ${className}`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">
                {isConnected ? `Connected: ${shortenAddr(address)}` : 'Not Connected'}
            </span>
        </div>
    );
};

/**
 * Main Wallet Connect UI Export
 * Contains all wallet-related components and utilities
 */
const WalletConnectUI = {
    ConnectButton,
    WalletWidget,
    WalletStatus,
    GradientButton,
    shortenAddr
};

export default WalletConnectUI;
