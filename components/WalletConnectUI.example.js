'use client';

import React from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import { 
    ConnectButton, 
    WalletWidget, 
    WalletStatus, 
    GradientButton,
    shortenAddr 
} from './WalletConnectUI';

/**
 * Example component demonstrating all WalletConnectUI components
 */
export default function WalletConnectExample() {
    const { address, isConnected } = useAppKitAccount();

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
            <div className="max-w-4xl mx-auto space-y-8">
                
                {/* Header */}
                <div className="text-center">
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        Wallet Connect UI Examples
                    </h1>
                    <p className="text-lg text-gray-600 dark:text-gray-300">
                        Demonstration of reusable wallet connection components
                    </p>
                </div>

                {/* Status Bar */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                            Connection Status
                        </h2>
                        <WalletStatus />
                    </div>
                </div>

                {/* Component Examples Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    
                    {/* Basic Connect Button */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Basic Connect Button
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                            Simple wallet connect button with gradient styling.
                        </p>
                        <ConnectButton className="w-full" />
                    </div>

                    {/* Connect Button Variant 2 */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Connect Button (Variant 2)
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                            Connect button with gradient border styling.
                        </p>
                        <ConnectButton variant={2} className="w-full" />
                    </div>

                    {/* Wallet Widget */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Wallet Widget
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                            Full-featured wallet widget with address display and copy functionality.
                        </p>
                        <WalletWidget />
                    </div>

                    {/* Custom Gradient Buttons */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Custom Gradient Buttons
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                            Reusable gradient buttons for other actions.
                        </p>
                        <div className="space-y-3">
                            <GradientButton variant={1} className="w-full">
                                Primary Action
                            </GradientButton>
                            <GradientButton variant={2} className="w-full">
                                Secondary Action
                            </GradientButton>
                        </div>
                    </div>
                </div>

                {/* Connected State Example */}
                {isConnected && (
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 border border-green-200 dark:border-green-700">
                        <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-4">
                            🎉 Wallet Connected Successfully!
                        </h3>
                        <div className="space-y-2 text-green-800 dark:text-green-200">
                            <p><strong>Address:</strong> {address}</p>
                            <p><strong>Shortened:</strong> {shortenAddr(address)}</p>
                            <p><strong>Status:</strong> Connected and ready for transactions</p>
                        </div>
                    </div>
                )}

                {/* Usage Code Examples */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        Usage Examples
                    </h3>
                    <div className="space-y-4">
                        <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Basic Connect Button:</h4>
                            <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-x-auto">
                                <code>{`import { ConnectButton } from './WalletConnectUI';

<ConnectButton className="w-full" />`}</code>
                            </pre>
                        </div>
                        
                        <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Wallet Widget:</h4>
                            <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-x-auto">
                                <code>{`import { WalletWidget } from './WalletConnectUI';

<WalletWidget 
    className="max-w-sm"
    variant={1}
/>`}</code>
                            </pre>
                        </div>

                        <div>
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Using Wallet Data:</h4>
                            <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-x-auto">
                                <code>{`import { useAppKitAccount } from '@reown/appkit/react';
import { shortenAddr } from './WalletConnectUI';

const { address, isConnected } = useAppKitAccount();

{isConnected && (
    <p>Connected: {shortenAddr(address)}</p>
)}`}</code>
                            </pre>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="text-center text-gray-600 dark:text-gray-400">
                    <p>
                        These components are ready to be copied and used in any React project with Reown AppKit.
                    </p>
                </div>
            </div>
        </div>
    );
}
