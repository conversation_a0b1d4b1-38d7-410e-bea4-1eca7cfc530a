# Wallet Connect UI Components

A comprehensive, reusable wallet connection UI library built with React and Reown AppKit (formerly WalletConnect). This component library provides everything you need to integrate wallet connectivity into your Web3 applications.

## Features

- 🔗 **Easy Wallet Connection**: One-click wallet connection using Reown AppKit
- 🎨 **Beautiful UI Components**: Pre-styled components with gradient designs and dark mode support
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
- 🔄 **Multiple Component Types**: From simple buttons to full-featured widgets
- 📋 **Address Management**: Copy addresses, shorten display, connection status
- 🌙 **Dark Mode Support**: Automatic dark mode detection and styling
- ⚡ **TypeScript Ready**: Fully typed components (can be converted to TypeScript)

## Components Included

### 1. ConnectButton
Simple wallet connect button that shows the wallet address when connected.

### 2. WalletWidget
Full-featured wallet widget with connect/disconnect functionality, address display, and copy functionality.

### 3. WalletStatus
Simple status indicator showing connection state.

### 4. GradientButton
Reusable gradient button component with multiple variants.

### 5. Utility Functions
- `shortenAddr()`: Utility to shorten wallet addresses

## Installation & Setup

### Prerequisites

```bash
npm install @reown/appkit @reown/appkit-adapter-ethers @reown/appkit/react
npm install @fortawesome/react-fontawesome @fortawesome/free-solid-svg-icons @fortawesome/fontawesome-svg-core
```

### 1. Copy the Component File

Copy `WalletConnectUI.js` to your project's components directory.

### 2. Setup AppKit Context

Create an AppKit context file (e.g., `context/AppKit.js`):

```javascript
'use client';

import { createAppKit } from '@reown/appkit/react';
import { EthersAdapter } from '@reown/appkit-adapter-ethers';
import { mainnet, sepolia } from '@reown/appkit/networks';

const projectId = 'YOUR_WALLETCONNECT_PROJECT_ID'; // Get from https://cloud.walletconnect.com

const metadata = {
    name: 'Your App Name',
    description: 'Your app description',
    icon: ['https://your-app.com/icon.png'],
    url: 'https://your-app.com',    
};

export const modal = createAppKit({
    adapters: [new EthersAdapter()],
    metadata,
    networks: [mainnet, sepolia], // Add your networks
    projectId,
    features: {
        analytics: true,
        onramp: false,
        swaps: false,
        smartSessions: false,
        email: false,
        socials: false,
    },
});

export function AppKit({ children }) {
    return <>{children}</>;
}
```

### 3. Wrap Your App

Wrap your app with the AppKit provider:

```javascript
// app/layout.js or pages/_app.js
import { AppKit } from '../context/AppKit';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <AppKit>
          {children}
        </AppKit>
      </body>
    </html>
  );
}
```

## Usage Examples

### Basic Connect Button

```javascript
import { ConnectButton } from './components/WalletConnectUI';

function MyComponent() {
    return (
        <div>
            <ConnectButton className="w-full" />
        </div>
    );
}
```

### Advanced Wallet Widget

```javascript
import { WalletWidget } from './components/WalletConnectUI';

function MyComponent() {
    return (
        <div>
            <WalletWidget 
                className="max-w-sm"
                showBalance={true}
                showNetwork={true}
                variant={1}
            />
        </div>
    );
}
```

### Wallet Status Indicator

```javascript
import { WalletStatus } from './components/WalletConnectUI';

function Header() {
    return (
        <header>
            <WalletStatus className="ml-auto" />
        </header>
    );
}
```

### Using Wallet Data in Your Components

```javascript
import { useAppKitAccount, useDisconnect } from '@reown/appkit/react';
import { shortenAddr } from './components/WalletConnectUI';

function MyComponent() {
    const { address, isConnected } = useAppKitAccount();
    const { disconnect } = useDisconnect();

    if (!isConnected) {
        return <ConnectButton />;
    }

    return (
        <div>
            <p>Connected: {shortenAddr(address)}</p>
            <button onClick={() => disconnect()}>Disconnect</button>
        </div>
    );
}
```

## Customization

### Styling

The components use Tailwind CSS classes. You can customize the styling by:

1. **Modifying the component classes directly**
2. **Using the className prop** to add additional styles
3. **Overriding CSS variables** for gradient colors

### Gradient Button Variants

```javascript
// Variant 1: Solid gradient background
<GradientButton variant={1}>Connect</GradientButton>

// Variant 2: Gradient border with transparent background
<GradientButton variant={2}>Connect</GradientButton>
```

### Dark Mode

Dark mode is automatically detected by checking for the `dark` class on the `document.documentElement`. Ensure your app implements dark mode by toggling this class.

## Dependencies

- React 18+
- @reown/appkit
- @reown/appkit-adapter-ethers
- @reown/appkit/react
- @fortawesome/react-fontawesome
- @fortawesome/free-solid-svg-icons
- Tailwind CSS (for styling)

## Browser Support

- Chrome/Chromium 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## License

This component library is provided as-is for reuse in your projects. Feel free to modify and adapt it to your needs.

## Support

For issues related to Reown AppKit, visit: https://docs.reown.com/appkit
For component-specific issues, refer to the source code and customize as needed.
