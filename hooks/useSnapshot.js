import { useEffect, useState } from "react";
import { ethers, JsonRpcProvider, Contract } from "ethers";
import { tokenAbi } from "../context/AppKit";
import BigNumber from "bignumber.js";

export default function useTakeSnapshot({
    rpcUrl,
    tokenAddress,
    fromBlock = 0,
    toBlock = "latest",
}) {
  const [snapshot, setSnapshot] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    let cancelled = false;

    async function fetchSnapshot() {
        setIsLoading(true);
        setError(null);

        try {
            const provider = new JsonRpcProvider(rpcUrl);
            const tokenContract = new Contract(tokenAddress, tokenAbi, provider);

            if(toBlock === "latest") {
                fromBlock = await provider.getBlockNumber() - 100000;
            }
           
            const logs = await tokenContract.queryFilter(tokenContract.filters.Transfer(), fromBlock, toBlock);

            const balances = new Map();
            const counts = new Map();

            const inc = (address, amount) => {
                const prev = balances.get(address) ?? BigNumber(0);
                balances.set(address, prev.plus(amount));
            }

            const incCount = (address) => {
                const prev = counts.get(address) ?? 0;
                counts.set(address, prev + 1);
            }

            const dec = (address, amount) => {
                const prev = balances.get(address) ?? BigNumber(0);
                balances.set(address, prev.minus(amount));
            }

            for(const log of logs) {
                const [from, to, value] = log.args;
                if (from !== ethers.ZeroAddress) dec(from, value);
                if (to !== ethers.ZeroAddress) inc(to, value);
                if (to !== ethers.ZeroAddress) incCount(to);
            }

            const snapshot = [];
            balances.forEach((balance, address) => {
                if(balance.gt(0)) {
                    snapshot.push({
                        address: address,
                        balance: balance.toString(),
                        count: counts.get(address) ?? 0
                    });
                }
            });

            if(!cancelled) {
                setSnapshot(snapshot);
            }
        } catch (error) {
            if(!cancelled) {
                setError(error);
            }
        } finally {
            if(!cancelled) {
            setIsLoading(false);
            }
        }
    }

    fetchSnapshot();

    return () => {
        cancelled = true;
    }
  }, [rpcUrl, tokenAddress, fromBlock, toBlock]);

  return { snapshot, isLoading, error };
}