import { useState, useEffect } from 'react';
import { Contract, JsonRpcProvider } from 'ethers';
import { presaleAddr, presaleAbi, rpcUrls } from '@/context/AppKit';

export default function useWithdrawls({
    rpcUrl,
    chainId,
}) {
    const [withdrawls, setWithdrawls] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchWithdrawls = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const provider = new JsonRpcProvider(rpcUrl);
                const presaleContract = new Contract(presaleAddr[chainId], presaleAbi, provider);

                const fromBlock = await provider.getBlockNumber() - 100000;

                const logsETH = await presaleContract.queryFilter(presaleContract.filters.BuyTokenETH(), fromBlock, "latest");
                const logsUSDT = await presaleContract.queryFilter(presaleContract.filters.BuyTokenUSDT(), fromBlock, "latest");

                const prepared = [];

                // Verarbeite ETH Logs mit Promise.all für korrekte asynchrone Behandlung
                const ethPromises = logsETH.map(async log => {
                    const { user, ethAmount, tokenAmount, refcode } = log.args;
                    const block = await provider.getBlock(log.blockNumber);
                    const timestamp = block.timestamp;

                    return {
                        txHash: log.transactionHash,
                        user,
                        ethAmount,
                        tokenAmount,
                        refcode,
                        status: "completed",
                        timestamp
                    };
                });

                // Verarbeite USDT Logs mit Promise.all für korrekte asynchrone Behandlung
                const usdtPromises = logsUSDT.map(async log => {
                    const { user, usdtAmount, tokenAmount, refcode } = log.args;
                    const block = await provider.getBlock(log.blockNumber);
                    const timestamp = block.timestamp;

                    return {
                        txHash: log.transactionHash,
                        user,
                        usdtAmount,
                        tokenAmount,
                        refcode,
                        status: "completed",
                        timestamp
                    };
                });

                // Warte auf alle asynchronen Operationen
                const [ethResults, usdtResults] = await Promise.all([
                    Promise.all(ethPromises),
                    Promise.all(usdtPromises)
                ]);

                // Füge alle Ergebnisse zum prepared Array hinzu
                prepared.push(...ethResults, ...usdtResults);

                setWithdrawls(prepared);
            } catch (error) {
                console.error("error", error);
                setError(error);
            } finally {
                setIsLoading(false);
            }
        }

        fetchWithdrawls();
    }, [rpcUrl, chainId]);

    return { withdrawls, isLoading, error };
}