import { useState, useEffect } from 'react'
import { JsonRpcProvider } from 'ethers'
import BigNumber from 'bignumber.js'

/**
 * useFeeHistory
 * @param {object}   options
 * @param {string}   options.rpcUrl         – Deine RPC-URL (z.B. Infura/Alchemy)
 * @param {number}   [options.blockCount=30]– An<PERSON>hl der letzten Blocks
 * @param {number[]} [options.percentiles=[10,50,90]] – Welche Percentiles der Priority Fee
 */
export function useGasData({
  rpcUrl,
  blockCount = 31,
  percentiles = [10, 50, 90],
}) {
  const [data, setData] = useState({
    baseFeePerGas: [],
    gasUsedRatio: [],
    reward: [],            // priority fees per percentile
    oldestBlock: null,
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (!rpcUrl) {
      setError(new Error('No rpcUrl provided'))
      setLoading(false)
      return
    }

    const provider = new JsonRpcProvider("https://eth.llamarpc.com")

    async function fetchFeeHistory() {
      setLoading(true)
      setError(null)
      try {
        // eth_feeHistory erwartet: [blockCountHex, "latest", percentiles]
        const blockCountHex = blockCount
        const result = await provider.send('eth_feeHistory', [
          blockCountHex,
          'latest',
          percentiles,
        ])

        // result sieht aus wie:
        // {
        //   oldestBlock: '0xABC123',
        //   baseFeePerGas: ['0x...', ...],
        //   gasUsedRatio: [0.6, ...],
        //   reward: [ ['0x...', ...], ... ]
        // }

        console.log(result)

        setData({
          oldestBlock: parseInt(result.oldestBlock, 16),
          baseFeePerGas: result.baseFeePerGas.map(x => new BigNumber(x)),
          gasUsedRatio: result.gasUsedRatio,
          reward: result.reward.map(arr => arr.map(x => new BigNumber(x))),
        })
      } catch (err) {
        console.error(err)
        setError(err)
      } finally {
        setLoading(false)
      }
    }

    fetchFeeHistory()
  }, [rpcUrl, blockCount, JSON.stringify(percentiles)])

  return { data, loading, error }
}