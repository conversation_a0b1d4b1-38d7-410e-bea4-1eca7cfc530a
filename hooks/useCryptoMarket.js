import { useState, useEffect } from 'react';

/**
 * Custom React hook to fetch current cryptocurrency market data.
 * Uses CoinGecko's public API under the hood.
 *
 * @param {Object} options
 * @param {string} options.vsCurrency - The fiat currency to compare against (default: 'usd').
 * @param {number} options.perPage - Number of coins per page (default: 10).
 * @param {number} options.page - Page number (default: 1).
 * @returns {{ data: Array, loading: boolean, error: Error|null }}
 */
export default function useCryptoMarket({ vsCurrency = 'usd', perPage = 20, page = 1 } = {}) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Abort controller for canceling fetch on unmount or param change
    const controller = new AbortController();
    const { signal } = controller;

    async function fetchData() {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(
          `https://api.coingecko.com/api/v3/coins/markets?vs_currency=${vsCurrency}&order=market_cap_desc&per_page=${perPage}&page=${page}&sparkline=false`,
          { signal }
        );

        if (!response.ok) {
          throw new Error(`Error fetching data: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        
        setData(result);
      } catch (err) {
        if (err.name !== 'AbortError') {
          setError(err);
        }
      } finally {
        setLoading(false);
      }
    }

    fetchData();

    return () => controller.abort();
  }, [vsCurrency, perPage, page]);

  return { data, loading, error };
}
