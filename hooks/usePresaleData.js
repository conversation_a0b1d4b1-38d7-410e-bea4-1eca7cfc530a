import { useEffect, useState } from "react";
import { ethers, JsonRpcProvider, Contract } from "ethers";
import { presaleAbi, presaleAddress } from "../context/AppKit";
import BigNumber from "bignumber.js";

export default function usePresaleData({
    presaleAddress,
    rpcUrl,
    chainId,
}) {
    const [presaleData, setPresaleData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchPresaleData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const provider = new JsonRpcProvider(rpcUrl, chainId);
                const presaleContract = new Contract(presaleAddress, presaleAbi, provider);
            
                const tokenPrice = await presaleContract.getTokenPriceUSDT();
                const tokenPriceETH = await presaleContract.getTokenPriceETH();
                const totalSoldUSDT = await presaleContract.totalSoldUSDT();
                const ethPrice = await presaleContract.getETHPrice();
                const tokensSold = await presaleContract.totalSold();
                const sellTokens = await presaleContract.SELL_SUPPLY();

                const tokensLeft = new BigNumber(sellTokens).minus(tokensSold);

                setPresaleData({
                    tokenPrice: tokenPrice,
                    tokenPriceETH: tokenPriceETH,
                    totalSoldUSDT: totalSoldUSDT,
                    ethPrice: ethPrice,
                    tokensLeft: tokensLeft,
                });
            } catch (error) {
                setError(error);
            } finally {
                setIsLoading(false);
            };
        };

        fetchPresaleData();
    }, [presaleAddress, rpcUrl, chainId]);

    return { presaleData, isLoading, error };
}